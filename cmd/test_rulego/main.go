// Package main RuleGo 集成测试
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"aiops/pkg/analysis"
	"aiops/pkg/rulego"
	"go.uber.org/zap"
)

func main() {
	// 初始化日志
	logger, err := zap.NewDevelopment()
	if err != nil {
		log.Fatal("初始化日志失败:", err)
	}
	defer logger.Sync()

	// 创建 RuleGo 引擎配置
	config := rulego.Config{
		PoolSize:     10,
		Debug:        true,
		EnableMetric: true,
		Database: rulego.DatabaseConfig{
			Driver: "sqlite",
			DSN:    "test.db",
		},
		Monitor: rulego.MonitorConfig{
			Enabled:         true,
			MetricsInterval: time.Minute,
			StatsRetention:  time.Hour,
		},
	}

	// 创建 RuleGo 引擎
	engine, err := rulego.NewAIOpsRuleEngine(config, logger)
	if err != nil {
		logger.Fatal("创建 RuleGo 引擎失败", zap.Error(err))
	}
	defer engine.Stop()

	// 创建数据分析器
	analyzer := &MockAnalyzer{logger: logger}

	// 创建集成配置
	integrationConfig := analysis.IntegrationConfig{
		ChainMappings: map[string]analysis.ChainMapping{
			"cpu_*": {
				MetricPattern: "cpu_*",
				ChainIDs:      []string{"cpu_monitoring_chain"},
				Conditions:    []string{"high_confidence"},
			},
		},
		DefaultChains: analysis.DefaultChains{
			CriticalChain:   "cpu_monitoring_chain",
			AnomalyChain:    "anomaly_detection_chain",
			ScalingChain:    "cpu_monitoring_chain",
			MonitoringChain: "memory_monitoring_chain",
		},
		TriggerConditions: analysis.TriggerConditions{
			AnomalyThreshold:  0.7,
			CriticalThreshold: 0.8,
			ScalingThreshold:  0.75,
			MinConfidence:     0.6,
		},
		Performance: analysis.PerformanceConfig{
			MaxConcurrentChains: 5,
			ChainTimeout:        30 * time.Second,
			BatchSize:           10,
			EnableCaching:       true,
		},
	}

	// 创建集成实例
	integration := analysis.NewRuleGoIntegration(engine, analyzer, integrationConfig, logger)

	// 创建默认规则链
	if err := integration.CreateDefaultChains(); err != nil {
		logger.Error("创建默认规则链失败", zap.Error(err))
	}

	// 加载示例规则链
	if err := loadExampleChains(engine, logger); err != nil {
		logger.Error("加载示例规则链失败", zap.Error(err))
	}

	// 测试指标处理
	testMetrics := []analysis.MetricData{
		{
			MetricName: "cpu_usage",
			Value:      85.5,
			Timestamp:  time.Now(),
			Labels: map[string]string{
				"host":    "web-server-01",
				"service": "web-app",
			},
			Source: "prometheus",
		},
		{
			MetricName: "memory_usage",
			Value:      92.3,
			Timestamp:  time.Now(),
			Labels: map[string]string{
				"host":    "web-server-01",
				"service": "web-app",
			},
			Source: "prometheus",
		},
		{
			MetricName: "disk_usage",
			Value:      45.2,
			Timestamp:  time.Now(),
			Labels: map[string]string{
				"host":    "web-server-01",
				"mount":   "/var/log",
			},
			Source: "prometheus",
		},
	}

	// 处理测试指标
	ctx := context.Background()
	logger.Info("开始处理测试指标")

	if err := integration.ProcessMetrics(ctx, testMetrics); err != nil {
		logger.Error("处理指标失败", zap.Error(err))
	}

	// 等待处理完成
	time.Sleep(5 * time.Second)

	// 获取执行统计
	stats := integration.GetExecutionStats()
	logger.Info("执行统计", zap.Any("stats", stats))

	// 测试单个规则链执行
	testSingleChain(engine, logger)

	logger.Info("测试完成")
}

// MockAnalyzer 模拟数据分析器
type MockAnalyzer struct {
	logger *zap.Logger
}

// Analyze 执行模拟分析
func (a *MockAnalyzer) Analyze(ctx context.Context, data analysis.MetricData) (*analysis.AnalysisResult, error) {
	a.logger.Debug("执行模拟分析", zap.String("metric", data.MetricName), zap.Float64("value", data.Value))

	result := &analysis.AnalysisResult{
		Type:      "threshold_analysis",
		Algorithm: "mock",
		Status:    "normal",
		Message:   "模拟分析完成",
		Severity:  "info",
		Details:   make(map[string]interface{}),
		Anomaly:   false,
		Trend:     "stable",
		Score:     0.5,
		Timestamp: time.Now(),
	}

	// 根据指标值模拟不同的分析结果
	switch data.MetricName {
	case "cpu_usage":
		if data.Value > 80 {
			result.Status = "warning"
			result.Severity = "warning"
			result.Anomaly = true
			result.Score = 0.8
			result.Message = "CPU使用率过高"
		}
		if data.Value > 90 {
			result.Status = "critical"
			result.Severity = "critical"
			result.Score = 0.95
			result.Message = "CPU使用率严重过高"
		}
	case "memory_usage":
		if data.Value > 85 {
			result.Status = "warning"
			result.Severity = "warning"
			result.Anomaly = true
			result.Score = 0.75
			result.Message = "内存使用率过高"
		}
	}

	result.Details["current_value"] = data.Value
	result.Details["threshold_80"] = 80.0
	result.Details["threshold_90"] = 90.0

	return result, nil
}

// loadExampleChains 加载示例规则链
func loadExampleChains(engine *rulego.AIOpsRuleEngine, logger *zap.Logger) error {
	// 读取示例规则链配置
	data, err := os.ReadFile("examples/rulego_chains.json")
	if err != nil {
		logger.Warn("无法读取示例规则链文件", zap.Error(err))
		return nil // 不是致命错误
	}

	var config struct {
		Chains []*rulego.ChainDefinition `json:"chains"`
	}

	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析规则链配置失败: %w", err)
	}

	// 创建规则链
	for _, chain := range config.Chains {
		if err := engine.CreateChain(chain); err != nil {
			logger.Error("创建规则链失败",
				zap.String("chain_id", chain.ID),
				zap.Error(err))
		} else {
			logger.Info("成功创建规则链", zap.String("chain_id", chain.ID))
		}
	}

	return nil
}

// testSingleChain 测试单个规则链执行
func testSingleChain(engine *rulego.AIOpsRuleEngine, logger *zap.Logger) {
	logger.Info("测试单个规则链执行")

	// 构造测试消息
	msg := &rulego.AIOpsMessage{
		Type:      rulego.MetricMessage,
		Timestamp: time.Now(),
		Source:    "test",
		Data: map[string]interface{}{
			"metric_name": "cpu_usage",
			"value":       95.5,
			"timestamp":   time.Now(),
		},
		Context: map[string]interface{}{
			"test": true,
		},
		Analysis: &rulego.AnalysisResult{
			Type:      "threshold",
			Algorithm: "test",
			Status:    "critical",
			Message:   "测试严重告警",
			Severity:  "critical",
			Details: map[string]interface{}{
				"current_value": 95.5,
			},
			Anomaly: true,
			Score:   0.95,
		},
	}

	// 执行规则链
	result, err := engine.ExecuteChain("cpu_monitoring_chain", msg)
	if err != nil {
		logger.Error("执行规则链失败", zap.Error(err))
	} else {
		logger.Info("规则链执行成功",
			zap.Bool("success", result.Success),
			zap.Duration("duration", result.Duration),
			zap.String("message", result.Message))
	}
}
