// Package analysis 数据分析与 RuleGo 集成
package analysis

import (
	"context"
	"fmt"
	"time"

	"aiops/internal/model"
	"aiops/pkg/rulego"
	"go.uber.org/zap"
)

// RuleGoIntegration 数据分析与 RuleGo 集成
type RuleGoIntegration struct {
	ruleEngine *rulego.AIOpsRuleEngine
	analyzer   DataAnalyzer
	config     IntegrationConfig
	logger     *zap.Logger
}

// DataAnalyzer 数据分析器接口
type DataAnalyzer interface {
	Analyze(ctx context.Context, data MetricData) (*AnalysisResult, error)
}

// MetricData 指标数据
type MetricData struct {
	MetricName string                 `json:"metric_name"`
	Value      float64                `json:"value"`
	Timestamp  time.Time              `json:"timestamp"`
	Labels     map[string]string      `json:"labels,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
	Source     string                 `json:"source,omitempty"`
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	Type      string                 `json:"type"`      // 分析类型
	Algorithm string                 `json:"algorithm"` // 使用的算法
	Status    string                 `json:"status"`    // 分析状态
	Message   string                 `json:"message"`   // 分析结果描述
	Severity  string                 `json:"severity"`  // 严重程度
	Details   map[string]interface{} `json:"details"`   // 详细分析数据
	Anomaly   bool                   `json:"anomaly"`   // 是否异常
	Trend     string                 `json:"trend"`     // 趋势
	Score     float64                `json:"score"`     // 分数
	Timestamp time.Time              `json:"timestamp"` // 分析时间
}

// IntegrationConfig 集成配置
type IntegrationConfig struct {
	// 规则链映射配置
	ChainMappings map[string]ChainMapping `json:"chain_mappings"`
	
	// 默认规则链
	DefaultChains DefaultChains `json:"default_chains"`
	
	// 触发条件
	TriggerConditions TriggerConditions `json:"trigger_conditions"`
	
	// 性能配置
	Performance PerformanceConfig `json:"performance"`
}

// ChainMapping 规则链映射
type ChainMapping struct {
	MetricPattern string   `json:"metric_pattern"` // 指标名称模式
	ChainIDs      []string `json:"chain_ids"`      // 对应的规则链ID
	Conditions    []string `json:"conditions"`     // 触发条件
}

// DefaultChains 默认规则链
type DefaultChains struct {
	CriticalChain  string `json:"critical_chain"`
	AnomalyChain   string `json:"anomaly_chain"`
	ScalingChain   string `json:"scaling_chain"`
	MonitoringChain string `json:"monitoring_chain"`
}

// TriggerConditions 触发条件
type TriggerConditions struct {
	AnomalyThreshold    float64 `json:"anomaly_threshold"`
	CriticalThreshold   float64 `json:"critical_threshold"`
	ScalingThreshold    float64 `json:"scaling_threshold"`
	MinConfidence       float64 `json:"min_confidence"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	MaxConcurrentChains int           `json:"max_concurrent_chains"`
	ChainTimeout        time.Duration `json:"chain_timeout"`
	BatchSize           int           `json:"batch_size"`
	EnableCaching       bool          `json:"enable_caching"`
}

// NewRuleGoIntegration 创建 RuleGo 集成
func NewRuleGoIntegration(
	ruleEngine *rulego.AIOpsRuleEngine,
	analyzer DataAnalyzer,
	config IntegrationConfig,
	logger *zap.Logger,
) *RuleGoIntegration {
	return &RuleGoIntegration{
		ruleEngine: ruleEngine,
		analyzer:   analyzer,
		config:     config,
		logger:     logger,
	}
}

// ProcessMetrics 处理指标数据
func (ri *RuleGoIntegration) ProcessMetrics(ctx context.Context, metrics []MetricData) error {
	ri.logger.Info("开始处理指标数据", zap.Int("count", len(metrics)))
	
	for _, metric := range metrics {
		if err := ri.ProcessSingleMetric(ctx, metric); err != nil {
			ri.logger.Error("处理单个指标失败",
				zap.String("metric_name", metric.MetricName),
				zap.Error(err))
			// 继续处理其他指标
		}
	}
	
	return nil
}

// ProcessSingleMetric 处理单个指标
func (ri *RuleGoIntegration) ProcessSingleMetric(ctx context.Context, metric MetricData) error {
	// 1. 数据分析
	analysisResult, err := ri.analyzer.Analyze(ctx, metric)
	if err != nil {
		return fmt.Errorf("数据分析失败: %w", err)
	}
	
	// 2. 根据分析结果选择规则链
	chainIDs := ri.selectChains(metric, analysisResult)
	if len(chainIDs) == 0 {
		ri.logger.Debug("没有匹配的规则链", zap.String("metric_name", metric.MetricName))
		return nil
	}
	
	// 3. 构造 AIOps 消息
	msg := ri.buildAIOpsMessage(metric, analysisResult)
	
	// 4. 并行执行规则链
	for _, chainID := range chainIDs {
		go func(cid string) {
			if err := ri.executeChain(ctx, cid, msg); err != nil {
				ri.logger.Error("执行规则链失败",
					zap.String("chain_id", cid),
					zap.String("metric_name", metric.MetricName),
					zap.Error(err))
			}
		}(chainID)
	}
	
	return nil
}

// selectChains 根据分析结果选择规则链
func (ri *RuleGoIntegration) selectChains(metric MetricData, analysis *AnalysisResult) []string {
	var chainIDs []string
	
	// 1. 检查配置的映射规则
	for pattern, mapping := range ri.config.ChainMappings {
		if ri.matchPattern(metric.MetricName, pattern) {
			if ri.checkConditions(metric, analysis, mapping.Conditions) {
				chainIDs = append(chainIDs, mapping.ChainIDs...)
			}
		}
	}
	
	// 2. 根据分析结果选择默认规则链
	if len(chainIDs) == 0 {
		chainIDs = ri.selectDefaultChains(metric, analysis)
	}
	
	return chainIDs
}

// selectDefaultChains 选择默认规则链
func (ri *RuleGoIntegration) selectDefaultChains(metric MetricData, analysis *AnalysisResult) []string {
	var chainIDs []string
	
	// 严重情况
	if analysis.Severity == "critical" || analysis.Score > ri.config.TriggerConditions.CriticalThreshold {
		if ri.config.DefaultChains.CriticalChain != "" {
			chainIDs = append(chainIDs, ri.config.DefaultChains.CriticalChain)
		}
	}
	
	// 异常检测
	if analysis.Anomaly && analysis.Score > ri.config.TriggerConditions.AnomalyThreshold {
		if ri.config.DefaultChains.AnomalyChain != "" {
			chainIDs = append(chainIDs, ri.config.DefaultChains.AnomalyChain)
		}
	}
	
	// 扩缩容场景
	if ri.isScalingMetric(metric) && analysis.Score > ri.config.TriggerConditions.ScalingThreshold {
		if ri.config.DefaultChains.ScalingChain != "" {
			chainIDs = append(chainIDs, ri.config.DefaultChains.ScalingChain)
		}
	}
	
	// 默认监控链
	if len(chainIDs) == 0 && ri.config.DefaultChains.MonitoringChain != "" {
		chainIDs = append(chainIDs, ri.config.DefaultChains.MonitoringChain)
	}
	
	return chainIDs
}

// matchPattern 匹配指标名称模式
func (ri *RuleGoIntegration) matchPattern(metricName, pattern string) bool {
	// 简单的模式匹配，可以扩展为正则表达式
	if pattern == "*" {
		return true
	}
	if pattern == metricName {
		return true
	}
	// 可以添加更复杂的模式匹配逻辑
	return false
}

// checkConditions 检查触发条件
func (ri *RuleGoIntegration) checkConditions(metric MetricData, analysis *AnalysisResult, conditions []string) bool {
	for _, condition := range conditions {
		if !ri.evaluateCondition(metric, analysis, condition) {
			return false
		}
	}
	return true
}

// evaluateCondition 评估单个条件
func (ri *RuleGoIntegration) evaluateCondition(metric MetricData, analysis *AnalysisResult, condition string) bool {
	switch condition {
	case "anomaly":
		return analysis.Anomaly
	case "critical":
		return analysis.Severity == "critical"
	case "warning":
		return analysis.Severity == "warning"
	case "high_confidence":
		return analysis.Score > ri.config.TriggerConditions.MinConfidence
	default:
		return true
	}
}

// isScalingMetric 判断是否为扩缩容相关指标
func (ri *RuleGoIntegration) isScalingMetric(metric MetricData) bool {
	scalingMetrics := []string{
		"cpu_usage",
		"memory_usage",
		"request_rate",
		"response_time",
		"queue_length",
	}
	
	for _, sm := range scalingMetrics {
		if metric.MetricName == sm {
			return true
		}
	}
	
	return false
}

// buildAIOpsMessage 构造 AIOps 消息
func (ri *RuleGoIntegration) buildAIOpsMessage(metric MetricData, analysis *AnalysisResult) *rulego.AIOpsMessage {
	msgType := rulego.MetricMessage
	if analysis.Anomaly {
		msgType = rulego.AnomalyMessage
	}
	
	return &rulego.AIOpsMessage{
		Type:      msgType,
		Timestamp: time.Now(),
		Source:    "data_analysis",
		Data: map[string]interface{}{
			"metric_name":  metric.MetricName,
			"value":        metric.Value,
			"timestamp":    metric.Timestamp,
			"labels":       metric.Labels,
			"metadata":     metric.Metadata,
		},
		Context: map[string]interface{}{
			"source":      metric.Source,
			"processed_at": time.Now(),
		},
		Analysis: &rulego.AnalysisResult{
			Type:      analysis.Type,
			Algorithm: analysis.Algorithm,
			Status:    analysis.Status,
			Message:   analysis.Message,
			Severity:  analysis.Severity,
			Details:   analysis.Details,
			Anomaly:   analysis.Anomaly,
			Trend:     analysis.Trend,
			Score:     analysis.Score,
		},
		Metadata: map[string]string{
			"integration": "rulego",
			"version":     "1.0",
		},
	}
}

// executeChain 执行规则链
func (ri *RuleGoIntegration) executeChain(ctx context.Context, chainID string, msg *rulego.AIOpsMessage) error {
	// 设置超时
	timeout := ri.config.Performance.ChainTimeout
	if timeout == 0 {
		timeout = 30 * time.Second
	}
	
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	
	// 执行规则链
	result, err := ri.ruleEngine.ExecuteChain(chainID, msg)
	if err != nil {
		return err
	}
	
	ri.logger.Debug("规则链执行完成",
		zap.String("chain_id", chainID),
		zap.Bool("success", result.Success),
		zap.Duration("duration", result.Duration))
	
	return nil
}

// GetExecutionStats 获取执行统计
func (ri *RuleGoIntegration) GetExecutionStats() map[string]*rulego.ExecutionStats {
	// 获取所有规则链的执行统计
	return map[string]*rulego.ExecutionStats{} // 实际实现需要调用 ruleEngine 的方法
}

// LoadChains 加载规则链
func (ri *RuleGoIntegration) LoadChains(chainIDs []string) error {
	for _, chainID := range chainIDs {
		if err := ri.ruleEngine.LoadChain(chainID); err != nil {
			ri.logger.Error("加载规则链失败",
				zap.String("chain_id", chainID),
				zap.Error(err))
			return err
		}
	}
	return nil
}

// CreateDefaultChains 创建默认规则链
func (ri *RuleGoIntegration) CreateDefaultChains() error {
	// 创建默认的规则链定义
	chains := ri.buildDefaultChainDefinitions()
	
	for _, chain := range chains {
		if err := ri.ruleEngine.CreateChain(chain); err != nil {
			ri.logger.Error("创建默认规则链失败",
				zap.String("chain_id", chain.ID),
				zap.Error(err))
			return err
		}
	}
	
	return nil
}

// buildDefaultChainDefinitions 构建默认规则链定义
func (ri *RuleGoIntegration) buildDefaultChainDefinitions() []*rulego.ChainDefinition {
	return []*rulego.ChainDefinition{
		{
			ID:          "cpu_monitoring_chain",
			Name:        "CPU监控处理链",
			Description: "处理CPU使用率监控和告警",
			Version:     "1.0",
			RuleChain: map[string]interface{}{
				"ruleChain": map[string]interface{}{
					"name": "CPU监控处理链",
					"root": true,
					"nodes": []map[string]interface{}{
						{
							"id":   "input",
							"type": "input",
							"name": "监控数据输入",
						},
						{
							"id":   "threshold_check",
							"type": "aiops/threshold",
							"name": "CPU阈值检查",
							"configuration": map[string]interface{}{
								"metric_name": "cpu_usage",
								"thresholds": map[string]float64{
									"warning":  70.0,
									"critical": 90.0,
								},
								"operator": ">",
							},
						},
						{
							"id":   "alert",
							"type": "aiops/alert",
							"name": "CPU告警",
							"configuration": map[string]interface{}{
								"level":    "warning",
								"message":  "CPU使用率过高",
								"channels": []string{"email", "slack"},
							},
						},
					],
					"connections": []map[string]interface{}{
						{"from": "input", "to": "threshold_check"},
						{"from": "threshold_check", "to": "alert", "label": "warning"},
					},
				},
			},
		},
	}
}
