// Package nodes 阈值检查节点实现
package nodes

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// ThresholdNode 阈值检查节点
type ThresholdNode struct {
	logger *zap.Logger
}

// ThresholdConfig 阈值配置
type ThresholdConfig struct {
	MetricName   string             `json:"metric_name"`
	Thresholds   map[string]float64 `json:"thresholds"`   // warning, critical
	Operator     string             `json:"operator"`     // >, <, >=, <=, ==, !=
	CheckType    string             `json:"check_type"`   // single, range
	RangeMin     float64            `json:"range_min,omitempty"`
	RangeMax     float64            `json:"range_max,omitempty"`
	Unit         string             `json:"unit,omitempty"`
	Description  string             `json:"description,omitempty"`
}

// ThresholdResult 阈值检查结果
type ThresholdResult struct {
	Passed       bool                   `json:"passed"`
	Level        string                 `json:"level"`        // normal, warning, critical
	Message      string                 `json:"message"`
	CurrentValue float64                `json:"current_value"`
	Threshold    float64                `json:"threshold"`
	Operator     string                 `json:"operator"`
	Details      map[string]interface{} `json:"details"`
	CheckedAt    time.Time              `json:"checked_at"`
}

// Type 返回节点类型
func (n *ThresholdNode) Type() string {
	return "aiops/threshold"
}

// New 创建新的节点实例
func (n *ThresholdNode) New() types.Node {
	return &ThresholdNode{
		logger: n.logger,
	}
}

// Init 初始化节点
func (n *ThresholdNode) Init(ruleConfig types.Config, configuration types.Configuration) error {
	return nil
}

// OnMsg 处理消息
func (n *ThresholdNode) OnMsg(ctx types.RuleContext, msg types.RuleMsg) {
	startTime := time.Now()
	
	// 解析阈值配置
	var thresholdConfig ThresholdConfig
	if err := json.Unmarshal([]byte(ctx.GetNodeConfig().Configuration), &thresholdConfig); err != nil {
		n.logger.Error("解析阈值配置失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 解析消息数据
	var msgData map[string]interface{}
	if err := json.Unmarshal([]byte(msg.Data), &msgData); err != nil {
		n.logger.Error("解析消息数据失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 执行阈值检查
	result := n.checkThreshold(msgData, thresholdConfig, msg)
	
	// 构造输出消息
	outputMsg := msg.Copy()
	
	// 设置阈值检查结果到消息数据
	thresholdResult := map[string]interface{}{
		"threshold_check":  result,
		"processed_at":     time.Now(),
		"duration_ms":      time.Since(startTime).Milliseconds(),
		"original_data":    msgData,
	}
	
	resultBytes, err := json.Marshal(thresholdResult)
	if err != nil {
		n.logger.Error("序列化阈值检查结果失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	outputMsg.Data = string(resultBytes)
	outputMsg.Type = "threshold_result"
	
	// 设置元数据
	outputMsg.Metadata.PutValue("threshold_passed", result.Passed)
	outputMsg.Metadata.PutValue("threshold_level", result.Level)
	outputMsg.Metadata.PutValue("current_value", result.CurrentValue)
	
	// 根据检查结果选择输出链路
	if !result.Passed {
		if result.Level == "critical" {
			ctx.TellNext(outputMsg, "critical")
		} else if result.Level == "warning" {
			ctx.TellNext(outputMsg, "warning")
		} else {
			ctx.TellNext(outputMsg, "alert")
		}
	} else {
		ctx.TellSuccess(outputMsg)
	}
	
	n.logger.Debug("阈值检查完成",
		zap.String("metric_name", thresholdConfig.MetricName),
		zap.Bool("passed", result.Passed),
		zap.String("level", result.Level),
		zap.Float64("current_value", result.CurrentValue),
		zap.Duration("duration", time.Since(startTime)))
}

// Destroy 销毁节点
func (n *ThresholdNode) Destroy() {
	// 清理资源
}

// checkThreshold 执行阈值检查
func (n *ThresholdNode) checkThreshold(data map[string]interface{}, config ThresholdConfig, msg types.RuleMsg) *ThresholdResult {
	result := &ThresholdResult{
		Passed:    true,
		Level:     "normal",
		Message:   "阈值检查通过",
		Operator:  config.Operator,
		Details:   make(map[string]interface{}),
		CheckedAt: time.Now(),
	}
	
	// 提取指标值
	value, found := n.extractMetricValue(data, config.MetricName)
	if !found {
		result.Passed = false
		result.Level = "error"
		result.Message = fmt.Sprintf("未找到指标 %s 的值", config.MetricName)
		return result
	}
	
	result.CurrentValue = value
	
	// 根据检查类型执行不同的检查逻辑
	switch config.CheckType {
	case "range":
		return n.checkRangeThreshold(value, config, result)
	default:
		return n.checkSingleThreshold(value, config, result)
	}
}

// checkSingleThreshold 检查单一阈值
func (n *ThresholdNode) checkSingleThreshold(value float64, config ThresholdConfig, result *ThresholdResult) *ThresholdResult {
	// 检查严重阈值
	if criticalThreshold, ok := config.Thresholds["critical"]; ok {
		if n.compareValue(value, criticalThreshold, config.Operator) {
			result.Passed = false
			result.Level = "critical"
			result.Threshold = criticalThreshold
			result.Message = fmt.Sprintf("指标 %s 值 %.2f %s 严重阈值 %.2f", 
				config.MetricName, value, config.Operator, criticalThreshold)
			result.Details["threshold_type"] = "critical"
			return result
		}
	}
	
	// 检查警告阈值
	if warningThreshold, ok := config.Thresholds["warning"]; ok {
		if n.compareValue(value, warningThreshold, config.Operator) {
			result.Passed = false
			result.Level = "warning"
			result.Threshold = warningThreshold
			result.Message = fmt.Sprintf("指标 %s 值 %.2f %s 警告阈值 %.2f", 
				config.MetricName, value, config.Operator, warningThreshold)
			result.Details["threshold_type"] = "warning"
			return result
		}
	}
	
	// 检查通用阈值
	if threshold, ok := config.Thresholds["default"]; ok {
		if n.compareValue(value, threshold, config.Operator) {
			result.Passed = false
			result.Level = "warning"
			result.Threshold = threshold
			result.Message = fmt.Sprintf("指标 %s 值 %.2f %s 阈值 %.2f", 
				config.MetricName, value, config.Operator, threshold)
			result.Details["threshold_type"] = "default"
			return result
		}
	}
	
	result.Details["threshold_type"] = "normal"
	return result
}

// checkRangeThreshold 检查范围阈值
func (n *ThresholdNode) checkRangeThreshold(value float64, config ThresholdConfig, result *ThresholdResult) *ThresholdResult {
	if value < config.RangeMin || value > config.RangeMax {
		result.Passed = false
		result.Level = "warning"
		result.Message = fmt.Sprintf("指标 %s 值 %.2f 超出正常范围 [%.2f, %.2f]", 
			config.MetricName, value, config.RangeMin, config.RangeMax)
		result.Details["range_min"] = config.RangeMin
		result.Details["range_max"] = config.RangeMax
		result.Details["threshold_type"] = "range"
		
		// 判断严重程度
		if value < config.RangeMin {
			result.Details["violation_type"] = "below_min"
			result.Details["deviation"] = config.RangeMin - value
		} else {
			result.Details["violation_type"] = "above_max"
			result.Details["deviation"] = value - config.RangeMax
		}
		
		// 根据偏离程度判断严重级别
		deviation := result.Details["deviation"].(float64)
		rangeSize := config.RangeMax - config.RangeMin
		if deviation > rangeSize*0.5 { // 偏离超过范围大小的50%认为是严重
			result.Level = "critical"
		}
	}
	
	return result
}

// compareValue 比较值
func (n *ThresholdNode) compareValue(value, threshold float64, operator string) bool {
	switch operator {
	case ">":
		return value > threshold
	case "<":
		return value < threshold
	case ">=":
		return value >= threshold
	case "<=":
		return value <= threshold
	case "==":
		return value == threshold
	case "!=":
		return value != threshold
	default:
		n.logger.Warn("未知的比较操作符", zap.String("operator", operator))
		return false
	}
}

// extractMetricValue 提取指标值
func (n *ThresholdNode) extractMetricValue(data map[string]interface{}, metricName string) (float64, bool) {
	// 尝试从不同位置提取指标值
	
	// 1. 直接从 value 字段
	if value, ok := data["value"]; ok {
		if v, ok := value.(float64); ok {
			return v, true
		}
		if v, ok := value.(int); ok {
			return float64(v), true
		}
	}
	
	// 2. 从分析结果中提取
	if analysis, ok := data["analysis"].(map[string]interface{}); ok {
		if details, ok := analysis["details"].(map[string]interface{}); ok {
			if value, ok := details["current_value"].(float64); ok {
				return value, true
			}
		}
	}
	
	// 3. 从原始数据中提取
	if originalData, ok := data["original_data"].(map[string]interface{}); ok {
		if value, ok := originalData["value"]; ok {
			if v, ok := value.(float64); ok {
				return v, true
			}
		}
	}
	
	// 4. 从指定的指标名称字段提取
	if metricName != "" {
		if value, ok := data[metricName]; ok {
			if v, ok := value.(float64); ok {
				return v, true
			}
			if v, ok := value.(int); ok {
				return float64(v), true
			}
		}
	}
	
	return 0, false
}
