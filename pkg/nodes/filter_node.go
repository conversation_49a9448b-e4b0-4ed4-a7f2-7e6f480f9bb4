// Package nodes 指标过滤节点实现
package nodes

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// MetricFilterNode 指标过滤节点
type MetricFilterNode struct {
	logger *zap.Logger
}

// FilterConfig 过滤配置
type FilterConfig struct {
	MetricName string                 `json:"metric_name,omitempty"`
	Threshold  float64                `json:"threshold,omitempty"`
	Operator   string                 `json:"operator,omitempty"` // >, <, >=, <=, ==, !=
	Labels     map[string]string      `json:"labels,omitempty"`
	TimeRange  TimeRangeFilter        `json:"time_range,omitempty"`
	ValueRange ValueRangeFilter       `json:"value_range,omitempty"`
	Regex      RegexFilter            `json:"regex,omitempty"`
	Custom     map[string]interface{} `json:"custom,omitempty"`
}

// TimeRangeFilter 时间范围过滤
type TimeRangeFilter struct {
	StartTime time.Time `json:"start_time,omitempty"`
	EndTime   time.Time `json:"end_time,omitempty"`
	Duration  string    `json:"duration,omitempty"` // 如 "1h", "30m"
}

// ValueRangeFilter 值范围过滤
type ValueRangeFilter struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

// RegexFilter 正则表达式过滤
type RegexFilter struct {
	Field   string `json:"field"`   // 要匹配的字段
	Pattern string `json:"pattern"` // 正则表达式
}

// Type 返回节点类型
func (n *MetricFilterNode) Type() string {
	return "aiops/metricFilter"
}

// New 创建新的节点实例
func (n *MetricFilterNode) New() types.Node {
	return &MetricFilterNode{
		logger: n.logger,
	}
}

// Init 初始化节点
func (n *MetricFilterNode) Init(ruleConfig types.Config, configuration types.Configuration) error {
	return nil
}

// OnMsg 处理消息
func (n *MetricFilterNode) OnMsg(ctx types.RuleContext, msg types.RuleMsg) {
	startTime := time.Now()
	
	// 解析过滤配置
	var filterConfig FilterConfig
	if err := json.Unmarshal([]byte(ctx.GetNodeConfig().Configuration), &filterConfig); err != nil {
		n.logger.Error("解析过滤配置失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 解析消息数据
	var msgData map[string]interface{}
	if err := json.Unmarshal([]byte(msg.Data), &msgData); err != nil {
		n.logger.Error("解析消息数据失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 执行过滤检查
	passed, reason := n.checkFilter(msgData, filterConfig, msg)
	
	// 构造输出消息
	outputMsg := msg.Copy()
	
	// 添加过滤结果到消息数据
	filterResult := map[string]interface{}{
		"filter_passed":  passed,
		"filter_reason":  reason,
		"filter_config":  filterConfig,
		"processed_at":   time.Now(),
		"duration_ms":    time.Since(startTime).Milliseconds(),
		"original_data":  msgData,
	}
	
	resultBytes, err := json.Marshal(filterResult)
	if err != nil {
		n.logger.Error("序列化过滤结果失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	outputMsg.Data = string(resultBytes)
	outputMsg.Type = "filter_result"
	
	// 设置元数据
	outputMsg.Metadata.PutValue("filter_passed", passed)
	outputMsg.Metadata.PutValue("filter_reason", reason)
	
	if passed {
		// 过滤通过，发送到成功链路
		ctx.TellSuccess(outputMsg)
		n.logger.Debug("指标过滤通过",
			zap.String("reason", reason),
			zap.Duration("duration", time.Since(startTime)))
	} else {
		// 过滤不通过，发送到失败链路或直接丢弃
		ctx.TellNext(outputMsg, "filtered")
		n.logger.Debug("指标被过滤",
			zap.String("reason", reason),
			zap.Duration("duration", time.Since(startTime)))
	}
}

// Destroy 销毁节点
func (n *MetricFilterNode) Destroy() {
	// 清理资源
}

// checkFilter 检查过滤条件
func (n *MetricFilterNode) checkFilter(data map[string]interface{}, config FilterConfig, msg types.RuleMsg) (bool, string) {
	// 检查指标名称
	if config.MetricName != "" {
		if metricName, ok := msg.Metadata.GetValue("metric_name"); ok {
			if metricName.(string) != config.MetricName {
				return false, fmt.Sprintf("指标名称不匹配: 期望 %s, 实际 %s", config.MetricName, metricName)
			}
		} else {
			return false, "消息中缺少指标名称"
		}
	}
	
	// 检查阈值条件
	if config.Threshold != 0 && config.Operator != "" {
		value, found := n.extractMetricValue(data)
		if !found {
			return false, "未找到指标值"
		}
		
		if !n.checkThreshold(value, config.Threshold, config.Operator) {
			return false, fmt.Sprintf("阈值条件不满足: %.2f %s %.2f", value, config.Operator, config.Threshold)
		}
	}
	
	// 检查标签过滤
	if len(config.Labels) > 0 {
		if !n.checkLabels(data, config.Labels) {
			return false, "标签条件不满足"
		}
	}
	
	// 检查时间范围
	if !n.checkTimeRange(data, config.TimeRange) {
		return false, "时间范围条件不满足"
	}
	
	// 检查值范围
	if !n.checkValueRange(data, config.ValueRange) {
		return false, "值范围条件不满足"
	}
	
	// 检查正则表达式
	if config.Regex.Pattern != "" {
		if !n.checkRegex(data, config.Regex) {
			return false, "正则表达式条件不满足"
		}
	}
	
	// 检查自定义条件
	if len(config.Custom) > 0 {
		if !n.checkCustomConditions(data, config.Custom) {
			return false, "自定义条件不满足"
		}
	}
	
	return true, "所有过滤条件都满足"
}

// extractMetricValue 提取指标值
func (n *MetricFilterNode) extractMetricValue(data map[string]interface{}) (float64, bool) {
	// 尝试从不同位置提取指标值
	
	// 1. 直接从 value 字段
	if value, ok := data["value"]; ok {
		if v, ok := value.(float64); ok {
			return v, true
		}
		if v, ok := value.(int); ok {
			return float64(v), true
		}
		if v, ok := value.(string); ok {
			if parsed, err := strconv.ParseFloat(v, 64); err == nil {
				return parsed, true
			}
		}
	}
	
	// 2. 从分析结果中提取
	if analysis, ok := data["analysis"].(map[string]interface{}); ok {
		if details, ok := analysis["details"].(map[string]interface{}); ok {
			if value, ok := details["current_value"].(float64); ok {
				return value, true
			}
		}
	}
	
	// 3. 从原始数据中提取
	if originalData, ok := data["original_data"].(map[string]interface{}); ok {
		if value, ok := originalData["value"]; ok {
			if v, ok := value.(float64); ok {
				return v, true
			}
		}
	}
	
	return 0, false
}

// checkThreshold 检查阈值条件
func (n *MetricFilterNode) checkThreshold(value, threshold float64, operator string) bool {
	switch operator {
	case ">":
		return value > threshold
	case "<":
		return value < threshold
	case ">=":
		return value >= threshold
	case "<=":
		return value <= threshold
	case "==":
		return value == threshold
	case "!=":
		return value != threshold
	default:
		n.logger.Warn("未知的操作符", zap.String("operator", operator))
		return false
	}
}

// checkLabels 检查标签条件
func (n *MetricFilterNode) checkLabels(data map[string]interface{}, expectedLabels map[string]string) bool {
	// 从数据中提取标签
	var labels map[string]string
	
	if labelsData, ok := data["labels"]; ok {
		if l, ok := labelsData.(map[string]string); ok {
			labels = l
		} else if l, ok := labelsData.(map[string]interface{}); ok {
			labels = make(map[string]string)
			for k, v := range l {
				if s, ok := v.(string); ok {
					labels[k] = s
				}
			}
		}
	}
	
	// 检查所有期望的标签
	for key, expectedValue := range expectedLabels {
		if actualValue, exists := labels[key]; !exists || actualValue != expectedValue {
			return false
		}
	}
	
	return true
}

// checkTimeRange 检查时间范围
func (n *MetricFilterNode) checkTimeRange(data map[string]interface{}, timeRange TimeRangeFilter) bool {
	if timeRange.StartTime.IsZero() && timeRange.EndTime.IsZero() && timeRange.Duration == "" {
		return true // 没有时间范围限制
	}
	
	// 提取时间戳
	var timestamp time.Time
	if ts, ok := data["timestamp"]; ok {
		if t, ok := ts.(time.Time); ok {
			timestamp = t
		} else if t, ok := ts.(string); ok {
			if parsed, err := time.Parse(time.RFC3339, t); err == nil {
				timestamp = parsed
			}
		}
	}
	
	if timestamp.IsZero() {
		timestamp = time.Now() // 使用当前时间作为默认值
	}
	
	// 检查时间范围
	if !timeRange.StartTime.IsZero() && timestamp.Before(timeRange.StartTime) {
		return false
	}
	
	if !timeRange.EndTime.IsZero() && timestamp.After(timeRange.EndTime) {
		return false
	}
	
	// 检查持续时间
	if timeRange.Duration != "" {
		if duration, err := time.ParseDuration(timeRange.Duration); err == nil {
			if time.Since(timestamp) > duration {
				return false
			}
		}
	}
	
	return true
}

// checkValueRange 检查值范围
func (n *MetricFilterNode) checkValueRange(data map[string]interface{}, valueRange ValueRangeFilter) bool {
	if valueRange.Min == 0 && valueRange.Max == 0 {
		return true // 没有值范围限制
	}
	
	value, found := n.extractMetricValue(data)
	if !found {
		return false
	}
	
	return value >= valueRange.Min && value <= valueRange.Max
}

// checkRegex 检查正则表达式
func (n *MetricFilterNode) checkRegex(data map[string]interface{}, regexFilter RegexFilter) bool {
	if regexFilter.Pattern == "" {
		return true
	}
	
	// 提取要匹配的字段值
	var fieldValue string
	if value, ok := data[regexFilter.Field]; ok {
		if s, ok := value.(string); ok {
			fieldValue = s
		} else {
			fieldValue = fmt.Sprintf("%v", value)
		}
	}
	
	// 编译并匹配正则表达式
	regex, err := regexp.Compile(regexFilter.Pattern)
	if err != nil {
		n.logger.Error("正则表达式编译失败", zap.Error(err))
		return false
	}
	
	return regex.MatchString(fieldValue)
}

// checkCustomConditions 检查自定义条件
func (n *MetricFilterNode) checkCustomConditions(data map[string]interface{}, customConditions map[string]interface{}) bool {
	for key, expectedValue := range customConditions {
		// 支持嵌套字段访问，如 "analysis.status"
		actualValue := n.getNestedValue(data, key)
		
		if !n.compareValues(actualValue, expectedValue) {
			return false
		}
	}
	
	return true
}

// getNestedValue 获取嵌套字段值
func (n *MetricFilterNode) getNestedValue(data map[string]interface{}, path string) interface{} {
	parts := strings.Split(path, ".")
	current := data
	
	for i, part := range parts {
		if i == len(parts)-1 {
			return current[part]
		}
		
		if next, ok := current[part].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}
	
	return nil
}

// compareValues 比较值
func (n *MetricFilterNode) compareValues(actual, expected interface{}) bool {
	if actual == nil && expected == nil {
		return true
	}
	
	if actual == nil || expected == nil {
		return false
	}
	
	// 类型转换和比较
	actualStr := fmt.Sprintf("%v", actual)
	expectedStr := fmt.Sprintf("%v", expected)
	
	return actualStr == expectedStr
}
