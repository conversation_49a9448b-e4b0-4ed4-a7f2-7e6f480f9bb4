// Package nodes 数据转换节点实现
package nodes

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// DataTransformNode 数据转换节点
type DataTransformNode struct {
	logger *zap.Logger
}

// TransformConfig 转换配置
type TransformConfig struct {
	Transformations []Transformation       `json:"transformations"`
	OutputFormat    string                 `json:"output_format,omitempty"` // json, flat, nested
	FieldMappings   map[string]string      `json:"field_mappings,omitempty"`
	Filters         []string               `json:"filters,omitempty"`
	Aggregations    []Aggregation          `json:"aggregations,omitempty"`
	CustomScript    string                 `json:"custom_script,omitempty"`
	Parameters      map[string]interface{} `json:"parameters,omitempty"`
}

// Transformation 转换规则
type Transformation struct {
	Type       string                 `json:"type"`        // rename, calculate, format, extract, aggregate
	SourceField string                `json:"source_field"`
	TargetField string                `json:"target_field"`
	Operation   string                `json:"operation,omitempty"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
	Condition   string                 `json:"condition,omitempty"`
}

// Aggregation 聚合配置
type Aggregation struct {
	Type        string   `json:"type"`         // sum, avg, min, max, count
	Fields      []string `json:"fields"`
	GroupBy     []string `json:"group_by,omitempty"`
	OutputField string   `json:"output_field"`
}

// TransformResult 转换结果
type TransformResult struct {
	Success         bool                   `json:"success"`
	TransformedData map[string]interface{} `json:"transformed_data"`
	AppliedRules    []string               `json:"applied_rules"`
	Errors          []string               `json:"errors,omitempty"`
	Statistics      map[string]interface{} `json:"statistics,omitempty"`
	ProcessedAt     time.Time              `json:"processed_at"`
}

// Type 返回节点类型
func (n *DataTransformNode) Type() string {
	return "aiops/transform"
}

// New 创建新的节点实例
func (n *DataTransformNode) New() types.Node {
	return &DataTransformNode{
		logger: n.logger,
	}
}

// Init 初始化节点
func (n *DataTransformNode) Init(ruleConfig types.Config, configuration types.Configuration) error {
	return nil
}

// OnMsg 处理消息
func (n *DataTransformNode) OnMsg(ctx types.RuleContext, msg types.RuleMsg) {
	startTime := time.Now()
	
	// 解析转换配置
	var transformConfig TransformConfig
	if err := json.Unmarshal([]byte(ctx.GetNodeConfig().Configuration), &transformConfig); err != nil {
		n.logger.Error("解析转换配置失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 解析消息数据
	var msgData map[string]interface{}
	if err := json.Unmarshal([]byte(msg.Data), &msgData); err != nil {
		n.logger.Error("解析消息数据失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 执行数据转换
	result := n.transformData(msgData, transformConfig)
	
	// 构造输出消息
	outputMsg := msg.Copy()
	
	// 设置转换结果到消息数据
	transformResult := map[string]interface{}{
		"transform_result": result,
		"processed_at":     time.Now(),
		"duration_ms":      time.Since(startTime).Milliseconds(),
		"original_data":    msgData,
	}
	
	resultBytes, err := json.Marshal(transformResult)
	if err != nil {
		n.logger.Error("序列化转换结果失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	outputMsg.Data = string(resultBytes)
	outputMsg.Type = "transform_result"
	
	// 设置元数据
	outputMsg.Metadata.PutValue("transform_success", result.Success)
	outputMsg.Metadata.PutValue("applied_rules_count", len(result.AppliedRules))
	
	if result.Success {
		ctx.TellSuccess(outputMsg)
	} else {
		ctx.TellFailure(outputMsg, fmt.Errorf("数据转换失败: %v", result.Errors))
	}
	
	n.logger.Debug("数据转换完成",
		zap.Bool("success", result.Success),
		zap.Int("applied_rules", len(result.AppliedRules)),
		zap.Int("errors", len(result.Errors)),
		zap.Duration("duration", time.Since(startTime)))
}

// Destroy 销毁节点
func (n *DataTransformNode) Destroy() {
	// 清理资源
}

// transformData 执行数据转换
func (n *DataTransformNode) transformData(data map[string]interface{}, config TransformConfig) *TransformResult {
	result := &TransformResult{
		Success:         true,
		TransformedData: make(map[string]interface{}),
		AppliedRules:    []string{},
		Errors:          []string{},
		Statistics:      make(map[string]interface{}),
		ProcessedAt:     time.Now(),
	}
	
	// 复制原始数据作为起点
	for k, v := range data {
		result.TransformedData[k] = v
	}
	
	// 应用字段映射
	if len(config.FieldMappings) > 0 {
		n.applyFieldMappings(result, config.FieldMappings)
	}
	
	// 应用转换规则
	for _, transformation := range config.Transformations {
		if err := n.applyTransformation(result, transformation); err != nil {
			result.Errors = append(result.Errors, err.Error())
			result.Success = false
		} else {
			result.AppliedRules = append(result.AppliedRules, 
				fmt.Sprintf("%s:%s->%s", transformation.Type, transformation.SourceField, transformation.TargetField))
		}
	}
	
	// 应用过滤器
	if len(config.Filters) > 0 {
		n.applyFilters(result, config.Filters)
	}
	
	// 应用聚合
	if len(config.Aggregations) > 0 {
		n.applyAggregations(result, config.Aggregations)
	}
	
	// 格式化输出
	if config.OutputFormat != "" {
		n.formatOutput(result, config.OutputFormat)
	}
	
	// 统计信息
	result.Statistics["field_count"] = len(result.TransformedData)
	result.Statistics["transformation_count"] = len(result.AppliedRules)
	result.Statistics["error_count"] = len(result.Errors)
	
	return result
}

// applyFieldMappings 应用字段映射
func (n *DataTransformNode) applyFieldMappings(result *TransformResult, mappings map[string]string) {
	for oldField, newField := range mappings {
		if value, exists := result.TransformedData[oldField]; exists {
			result.TransformedData[newField] = value
			delete(result.TransformedData, oldField)
			result.AppliedRules = append(result.AppliedRules, fmt.Sprintf("rename:%s->%s", oldField, newField))
		}
	}
}

// applyTransformation 应用单个转换规则
func (n *DataTransformNode) applyTransformation(result *TransformResult, transformation Transformation) error {
	switch transformation.Type {
	case "rename":
		return n.renameField(result, transformation)
	case "calculate":
		return n.calculateField(result, transformation)
	case "format":
		return n.formatField(result, transformation)
	case "extract":
		return n.extractField(result, transformation)
	case "convert":
		return n.convertField(result, transformation)
	default:
		return fmt.Errorf("未知的转换类型: %s", transformation.Type)
	}
}

// renameField 重命名字段
func (n *DataTransformNode) renameField(result *TransformResult, transformation Transformation) error {
	if value, exists := result.TransformedData[transformation.SourceField]; exists {
		result.TransformedData[transformation.TargetField] = value
		delete(result.TransformedData, transformation.SourceField)
		return nil
	}
	return fmt.Errorf("源字段不存在: %s", transformation.SourceField)
}

// calculateField 计算字段
func (n *DataTransformNode) calculateField(result *TransformResult, transformation Transformation) error {
	switch transformation.Operation {
	case "add":
		return n.calculateAdd(result, transformation)
	case "multiply":
		return n.calculateMultiply(result, transformation)
	case "percentage":
		return n.calculatePercentage(result, transformation)
	case "round":
		return n.calculateRound(result, transformation)
	default:
		return fmt.Errorf("未知的计算操作: %s", transformation.Operation)
	}
}

// calculateAdd 加法计算
func (n *DataTransformNode) calculateAdd(result *TransformResult, transformation Transformation) error {
	sourceValue, exists := result.TransformedData[transformation.SourceField]
	if !exists {
		return fmt.Errorf("源字段不存在: %s", transformation.SourceField)
	}
	
	addValue, ok := transformation.Parameters["value"].(float64)
	if !ok {
		return fmt.Errorf("加法参数无效")
	}
	
	if numValue, ok := sourceValue.(float64); ok {
		result.TransformedData[transformation.TargetField] = numValue + addValue
		return nil
	}
	
	return fmt.Errorf("源字段不是数值类型")
}

// calculateMultiply 乘法计算
func (n *DataTransformNode) calculateMultiply(result *TransformResult, transformation Transformation) error {
	sourceValue, exists := result.TransformedData[transformation.SourceField]
	if !exists {
		return fmt.Errorf("源字段不存在: %s", transformation.SourceField)
	}
	
	multiplyValue, ok := transformation.Parameters["value"].(float64)
	if !ok {
		return fmt.Errorf("乘法参数无效")
	}
	
	if numValue, ok := sourceValue.(float64); ok {
		result.TransformedData[transformation.TargetField] = numValue * multiplyValue
		return nil
	}
	
	return fmt.Errorf("源字段不是数值类型")
}

// calculatePercentage 百分比计算
func (n *DataTransformNode) calculatePercentage(result *TransformResult, transformation Transformation) error {
	sourceValue, exists := result.TransformedData[transformation.SourceField]
	if !exists {
		return fmt.Errorf("源字段不存在: %s", transformation.SourceField)
	}
	
	totalValue, ok := transformation.Parameters["total"].(float64)
	if !ok {
		return fmt.Errorf("总数参数无效")
	}
	
	if numValue, ok := sourceValue.(float64); ok {
		percentage := (numValue / totalValue) * 100
		result.TransformedData[transformation.TargetField] = percentage
		return nil
	}
	
	return fmt.Errorf("源字段不是数值类型")
}

// calculateRound 四舍五入
func (n *DataTransformNode) calculateRound(result *TransformResult, transformation Transformation) error {
	sourceValue, exists := result.TransformedData[transformation.SourceField]
	if !exists {
		return fmt.Errorf("源字段不存在: %s", transformation.SourceField)
	}
	
	precision := 2 // 默认保留2位小数
	if p, ok := transformation.Parameters["precision"].(float64); ok {
		precision = int(p)
	}
	
	if numValue, ok := sourceValue.(float64); ok {
		multiplier := 1.0
		for i := 0; i < precision; i++ {
			multiplier *= 10
		}
		rounded := float64(int(numValue*multiplier+0.5)) / multiplier
		result.TransformedData[transformation.TargetField] = rounded
		return nil
	}
	
	return fmt.Errorf("源字段不是数值类型")
}

// formatField 格式化字段
func (n *DataTransformNode) formatField(result *TransformResult, transformation Transformation) error {
	sourceValue, exists := result.TransformedData[transformation.SourceField]
	if !exists {
		return fmt.Errorf("源字段不存在: %s", transformation.SourceField)
	}
	
	format, ok := transformation.Parameters["format"].(string)
	if !ok {
		return fmt.Errorf("格式参数无效")
	}
	
	switch format {
	case "uppercase":
		if strValue, ok := sourceValue.(string); ok {
			result.TransformedData[transformation.TargetField] = strings.ToUpper(strValue)
		}
	case "lowercase":
		if strValue, ok := sourceValue.(string); ok {
			result.TransformedData[transformation.TargetField] = strings.ToLower(strValue)
		}
	case "timestamp":
		if timeValue, ok := sourceValue.(time.Time); ok {
			result.TransformedData[transformation.TargetField] = timeValue.Unix()
		}
	default:
		return fmt.Errorf("未知的格式类型: %s", format)
	}
	
	return nil
}

// extractField 提取字段
func (n *DataTransformNode) extractField(result *TransformResult, transformation Transformation) error {
	sourceValue, exists := result.TransformedData[transformation.SourceField]
	if !exists {
		return fmt.Errorf("源字段不存在: %s", transformation.SourceField)
	}
	
	extractType, ok := transformation.Parameters["type"].(string)
	if !ok {
		return fmt.Errorf("提取类型参数无效")
	}
	
	switch extractType {
	case "substring":
		return n.extractSubstring(result, transformation, sourceValue)
	case "split":
		return n.extractSplit(result, transformation, sourceValue)
	default:
		return fmt.Errorf("未知的提取类型: %s", extractType)
	}
}

// extractSubstring 提取子字符串
func (n *DataTransformNode) extractSubstring(result *TransformResult, transformation Transformation, sourceValue interface{}) error {
	strValue, ok := sourceValue.(string)
	if !ok {
		return fmt.Errorf("源字段不是字符串类型")
	}
	
	start, ok1 := transformation.Parameters["start"].(float64)
	length, ok2 := transformation.Parameters["length"].(float64)
	
	if !ok1 || !ok2 {
		return fmt.Errorf("子字符串参数无效")
	}
	
	startIdx := int(start)
	lengthVal := int(length)
	
	if startIdx < 0 || startIdx >= len(strValue) {
		return fmt.Errorf("起始位置超出范围")
	}
	
	endIdx := startIdx + lengthVal
	if endIdx > len(strValue) {
		endIdx = len(strValue)
	}
	
	result.TransformedData[transformation.TargetField] = strValue[startIdx:endIdx]
	return nil
}

// extractSplit 分割字符串
func (n *DataTransformNode) extractSplit(result *TransformResult, transformation Transformation, sourceValue interface{}) error {
	strValue, ok := sourceValue.(string)
	if !ok {
		return fmt.Errorf("源字段不是字符串类型")
	}
	
	delimiter, ok1 := transformation.Parameters["delimiter"].(string)
	index, ok2 := transformation.Parameters["index"].(float64)
	
	if !ok1 || !ok2 {
		return fmt.Errorf("分割参数无效")
	}
	
	parts := strings.Split(strValue, delimiter)
	idx := int(index)
	
	if idx < 0 || idx >= len(parts) {
		return fmt.Errorf("索引超出范围")
	}
	
	result.TransformedData[transformation.TargetField] = parts[idx]
	return nil
}

// convertField 转换字段类型
func (n *DataTransformNode) convertField(result *TransformResult, transformation Transformation) error {
	sourceValue, exists := result.TransformedData[transformation.SourceField]
	if !exists {
		return fmt.Errorf("源字段不存在: %s", transformation.SourceField)
	}
	
	targetType, ok := transformation.Parameters["target_type"].(string)
	if !ok {
		return fmt.Errorf("目标类型参数无效")
	}
	
	switch targetType {
	case "string":
		result.TransformedData[transformation.TargetField] = fmt.Sprintf("%v", sourceValue)
	case "float":
		if strValue, ok := sourceValue.(string); ok {
			if floatValue, err := strconv.ParseFloat(strValue, 64); err == nil {
				result.TransformedData[transformation.TargetField] = floatValue
			} else {
				return fmt.Errorf("无法转换为浮点数: %s", strValue)
			}
		}
	case "int":
		if strValue, ok := sourceValue.(string); ok {
			if intValue, err := strconv.Atoi(strValue); err == nil {
				result.TransformedData[transformation.TargetField] = intValue
			} else {
				return fmt.Errorf("无法转换为整数: %s", strValue)
			}
		}
	default:
		return fmt.Errorf("未知的目标类型: %s", targetType)
	}
	
	return nil
}

// applyFilters 应用过滤器
func (n *DataTransformNode) applyFilters(result *TransformResult, filters []string) {
	filteredData := make(map[string]interface{})
	for _, field := range filters {
		if value, exists := result.TransformedData[field]; exists {
			filteredData[field] = value
		}
	}
	result.TransformedData = filteredData
	result.AppliedRules = append(result.AppliedRules, fmt.Sprintf("filter:%v", filters))
}

// applyAggregations 应用聚合
func (n *DataTransformNode) applyAggregations(result *TransformResult, aggregations []Aggregation) {
	for _, agg := range aggregations {
		switch agg.Type {
		case "count":
			result.TransformedData[agg.OutputField] = len(agg.Fields)
		case "sum":
			sum := 0.0
			for _, field := range agg.Fields {
				if value, exists := result.TransformedData[field]; exists {
					if numValue, ok := value.(float64); ok {
						sum += numValue
					}
				}
			}
			result.TransformedData[agg.OutputField] = sum
		}
		result.AppliedRules = append(result.AppliedRules, fmt.Sprintf("aggregate:%s", agg.Type))
	}
}

// formatOutput 格式化输出
func (n *DataTransformNode) formatOutput(result *TransformResult, format string) {
	switch format {
	case "flat":
		// 扁平化嵌套结构
		n.flattenData(result.TransformedData, "", result.TransformedData)
	case "nested":
		// 这里可以实现嵌套结构转换
	}
	result.AppliedRules = append(result.AppliedRules, fmt.Sprintf("format:%s", format))
}

// flattenData 扁平化数据
func (n *DataTransformNode) flattenData(data map[string]interface{}, prefix string, result map[string]interface{}) {
	for key, value := range data {
		newKey := key
		if prefix != "" {
			newKey = prefix + "." + key
		}
		
		if nestedMap, ok := value.(map[string]interface{}); ok {
			n.flattenData(nestedMap, newKey, result)
		} else {
			result[newKey] = value
		}
	}
}
