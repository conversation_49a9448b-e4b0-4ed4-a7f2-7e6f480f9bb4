// Package nodes 扩缩容节点实现
package nodes

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// AutoScaleNode 自动扩缩容节点
type AutoScaleNode struct {
	scaler K8sScaler
	logger *zap.Logger
}

// K8sScaler Kubernetes 扩缩容器接口
type K8sScaler interface {
	Scale(ctx context.Context, request ScaleRequest) (*ScaleResult, error)
}

// ScaleRequest 扩缩容请求
type ScaleRequest struct {
	Namespace      string            `json:"namespace"`
	ResourceType   string            `json:"resource_type"`   // deployment, statefulset, etc.
	ResourceName   string            `json:"resource_name"`
	ScaleType      string            `json:"scale_type"`      // horizontal, vertical
	TargetReplicas int32             `json:"target_replicas,omitempty"`
	CPURequest     string            `json:"cpu_request,omitempty"`
	MemoryRequest  string            `json:"memory_request,omitempty"`
	CPULimit       string            `json:"cpu_limit,omitempty"`
	MemoryLimit    string            `json:"memory_limit,omitempty"`
	Reason         string            `json:"reason"`
	Metadata       map[string]string `json:"metadata,omitempty"`
}

// ScaleResult 扩缩容结果
type ScaleResult struct {
	Success        bool              `json:"success"`
	Message        string            `json:"message"`
	PreviousScale  int32             `json:"previous_scale"`
	CurrentScale   int32             `json:"current_scale"`
	ScaleTime      time.Time         `json:"scale_time"`
	Metadata       map[string]string `json:"metadata,omitempty"`
}

// ScaleConfig 扩缩容配置
type ScaleConfig struct {
	Namespace      string            `json:"namespace"`
	ResourceType   string            `json:"resource_type"`
	ResourceName   string            `json:"resource_name"`
	ScaleType      string            `json:"scale_type"`
	MinReplicas    int32             `json:"min_replicas"`
	MaxReplicas    int32             `json:"max_replicas"`
	ScaleUpStep    int32             `json:"scale_up_step"`
	ScaleDownStep  int32             `json:"scale_down_step"`
	CooldownPeriod time.Duration     `json:"cooldown_period"`
	Conditions     ScaleConditions   `json:"conditions"`
	Resources      ResourceLimits    `json:"resources,omitempty"`
	Metadata       map[string]string `json:"metadata,omitempty"`
}

// ScaleConditions 扩缩容条件
type ScaleConditions struct {
	ScaleUpThreshold   float64 `json:"scale_up_threshold"`
	ScaleDownThreshold float64 `json:"scale_down_threshold"`
	MetricName         string  `json:"metric_name"`
}

// ResourceLimits 资源限制
type ResourceLimits struct {
	CPURequest    string `json:"cpu_request,omitempty"`
	MemoryRequest string `json:"memory_request,omitempty"`
	CPULimit      string `json:"cpu_limit,omitempty"`
	MemoryLimit   string `json:"memory_limit,omitempty"`
}

// Type 返回节点类型
func (n *AutoScaleNode) Type() string {
	return "aiops/scale"
}

// New 创建新的节点实例
func (n *AutoScaleNode) New() types.Node {
	return &AutoScaleNode{
		scaler: n.scaler,
		logger: n.logger,
	}
}

// Init 初始化节点
func (n *AutoScaleNode) Init(ruleConfig types.Config, configuration types.Configuration) error {
	// 初始化扩缩容器
	if n.scaler == nil {
		n.scaler = NewDefaultK8sScaler(n.logger)
	}
	
	return nil
}

// OnMsg 处理消息
func (n *AutoScaleNode) OnMsg(ctx types.RuleContext, msg types.RuleMsg) {
	startTime := time.Now()
	
	// 解析扩缩容配置
	var scaleConfig ScaleConfig
	if err := json.Unmarshal([]byte(ctx.GetNodeConfig().Configuration), &scaleConfig); err != nil {
		n.logger.Error("解析扩缩容配置失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 解析消息数据
	var msgData map[string]interface{}
	if err := json.Unmarshal([]byte(msg.Data), &msgData); err != nil {
		n.logger.Error("解析消息数据失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 检查是否需要扩缩容
	scaleDecision := n.makeScaleDecision(msgData, scaleConfig)
	if scaleDecision == nil {
		n.logger.Debug("不需要扩缩容")
		ctx.TellSuccess(msg)
		return
	}
	
	// 执行扩缩容
	scaleCtx := context.Background()
	result, err := n.scaler.Scale(scaleCtx, *scaleDecision)
	if err != nil {
		n.logger.Error("扩缩容执行失败",
			zap.String("namespace", scaleDecision.Namespace),
			zap.String("resource", scaleDecision.ResourceName),
			zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 构造输出消息
	outputMsg := msg.Copy()
	
	// 设置扩缩容结果到消息数据
	scaleResult := map[string]interface{}{
		"scale_executed":   true,
		"scale_success":    result.Success,
		"scale_message":    result.Message,
		"previous_scale":   result.PreviousScale,
		"current_scale":    result.CurrentScale,
		"scale_time":       result.ScaleTime,
		"scale_request":    scaleDecision,
		"duration_ms":      time.Since(startTime).Milliseconds(),
		"original_data":    msgData,
	}
	
	resultBytes, err := json.Marshal(scaleResult)
	if err != nil {
		n.logger.Error("序列化扩缩容结果失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	outputMsg.Data = string(resultBytes)
	outputMsg.Type = "scale_result"
	
	// 设置元数据
	outputMsg.Metadata.PutValue("scale_executed", true)
	outputMsg.Metadata.PutValue("scale_success", result.Success)
	outputMsg.Metadata.PutValue("namespace", scaleDecision.Namespace)
	outputMsg.Metadata.PutValue("resource_name", scaleDecision.ResourceName)
	outputMsg.Metadata.PutValue("current_scale", result.CurrentScale)
	
	ctx.TellSuccess(outputMsg)
	
	n.logger.Info("扩缩容执行完成",
		zap.String("namespace", scaleDecision.Namespace),
		zap.String("resource", scaleDecision.ResourceName),
		zap.Bool("success", result.Success),
		zap.Int32("previous_scale", result.PreviousScale),
		zap.Int32("current_scale", result.CurrentScale),
		zap.Duration("duration", time.Since(startTime)))
}

// Destroy 销毁节点
func (n *AutoScaleNode) Destroy() {
	// 清理资源
}

// makeScaleDecision 做出扩缩容决策
func (n *AutoScaleNode) makeScaleDecision(data map[string]interface{}, config ScaleConfig) *ScaleRequest {
	// 从分析结果获取指标值
	var metricValue float64
	var found bool
	
	if analysis, ok := data["analysis"].(map[string]interface{}); ok {
		if details, ok := analysis["details"].(map[string]interface{}); ok {
			if value, ok := details["current_value"].(float64); ok {
				metricValue = value
				found = true
			}
		}
	}
	
	if !found {
		n.logger.Debug("未找到指标值，跳过扩缩容决策")
		return nil
	}
	
	// 检查扩容条件
	if metricValue > config.Conditions.ScaleUpThreshold {
		return &ScaleRequest{
			Namespace:      config.Namespace,
			ResourceType:   config.ResourceType,
			ResourceName:   config.ResourceName,
			ScaleType:      config.ScaleType,
			TargetReplicas: config.ScaleUpStep, // 这里应该是当前副本数 + 扩容步长
			Reason:         fmt.Sprintf("指标 %s 值 %.2f 超过扩容阈值 %.2f", config.Conditions.MetricName, metricValue, config.Conditions.ScaleUpThreshold),
			Metadata:       config.Metadata,
		}
	}
	
	// 检查缩容条件
	if metricValue < config.Conditions.ScaleDownThreshold {
		return &ScaleRequest{
			Namespace:      config.Namespace,
			ResourceType:   config.ResourceType,
			ResourceName:   config.ResourceName,
			ScaleType:      config.ScaleType,
			TargetReplicas: -config.ScaleDownStep, // 负数表示缩容
			Reason:         fmt.Sprintf("指标 %s 值 %.2f 低于缩容阈值 %.2f", config.Conditions.MetricName, metricValue, config.Conditions.ScaleDownThreshold),
			Metadata:       config.Metadata,
		}
	}
	
	return nil
}

// DefaultK8sScaler 默认 Kubernetes 扩缩容器实现
type DefaultK8sScaler struct {
	logger *zap.Logger
}

// NewDefaultK8sScaler 创建默认 Kubernetes 扩缩容器
func NewDefaultK8sScaler(logger *zap.Logger) *DefaultK8sScaler {
	return &DefaultK8sScaler{
		logger: logger,
	}
}

// Scale 执行扩缩容
func (s *DefaultK8sScaler) Scale(ctx context.Context, request ScaleRequest) (*ScaleResult, error) {
	s.logger.Info("执行扩缩容",
		zap.String("namespace", request.Namespace),
		zap.String("resource_type", request.ResourceType),
		zap.String("resource_name", request.ResourceName),
		zap.String("scale_type", request.ScaleType),
		zap.Int32("target_replicas", request.TargetReplicas),
		zap.String("reason", request.Reason))
	
	// 这里应该实现真实的 Kubernetes API 调用
	// 为了演示，我们模拟扩缩容过程
	
	// 模拟获取当前副本数
	currentReplicas := int32(3) // 假设当前有3个副本
	
	var newReplicas int32
	if request.TargetReplicas > 0 {
		// 扩容
		newReplicas = currentReplicas + request.TargetReplicas
	} else {
		// 缩容
		newReplicas = currentReplicas + request.TargetReplicas // TargetReplicas 是负数
		if newReplicas < 1 {
			newReplicas = 1 // 最少保持1个副本
		}
	}
	
	// 模拟扩缩容操作
	time.Sleep(100 * time.Millisecond) // 模拟API调用延迟
	
	result := &ScaleResult{
		Success:       true,
		Message:       fmt.Sprintf("成功将 %s/%s 从 %d 个副本调整为 %d 个副本", request.Namespace, request.ResourceName, currentReplicas, newReplicas),
		PreviousScale: currentReplicas,
		CurrentScale:  newReplicas,
		ScaleTime:     time.Now(),
		Metadata:      request.Metadata,
	}
	
	s.logger.Info("扩缩容完成",
		zap.String("namespace", request.Namespace),
		zap.String("resource_name", request.ResourceName),
		zap.Int32("previous_scale", result.PreviousScale),
		zap.Int32("current_scale", result.CurrentScale))
	
	return result, nil
}
