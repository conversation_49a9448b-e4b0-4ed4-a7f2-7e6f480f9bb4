// Package nodes 告警节点实现
package nodes

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// AlertNode 告警节点
type AlertNode struct {
	alertManager AlertManager
	logger       *zap.Logger
}

// AlertManager 告警管理器接口
type AlertManager interface {
	SendAlert(ctx context.Context, alert Alert) error
}

// Alert 告警信息
type Alert struct {
	ID          string                 `json:"id"`
	Level       AlertLevel             `json:"level"`
	Title       string                 `json:"title"`
	Message     string                 `json:"message"`
	Source      string                 `json:"source"`
	MetricName  string                 `json:"metric_name,omitempty"`
	MetricValue float64                `json:"metric_value,omitempty"`
	Channels    []string               `json:"channels"`
	Data        map[string]interface{} `json:"data,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
	Tags        map[string]string      `json:"tags,omitempty"`
}

// AlertLevel 告警级别
type AlertLevel string

const (
	AlertLevelInfo     AlertLevel = "info"
	AlertLevelWarning  AlertLevel = "warning"
	AlertLevelError    AlertLevel = "error"
	AlertLevelCritical AlertLevel = "critical"
)

// AlertConfig 告警配置
type AlertConfig struct {
	Level       AlertLevel             `json:"level"`
	Title       string                 `json:"title"`
	Message     string                 `json:"message"`
	Channels    []string               `json:"channels"`
	Template    string                 `json:"template,omitempty"`
	Throttle    time.Duration          `json:"throttle,omitempty"`
	Tags        map[string]string      `json:"tags,omitempty"`
	Conditions  map[string]interface{} `json:"conditions,omitempty"`
}

// Type 返回节点类型
func (n *AlertNode) Type() string {
	return "aiops/alert"
}

// New 创建新的节点实例
func (n *AlertNode) New() types.Node {
	return &AlertNode{
		alertManager: n.alertManager,
		logger:       n.logger,
	}
}

// Init 初始化节点
func (n *AlertNode) Init(ruleConfig types.Config, configuration types.Configuration) error {
	// 初始化告警管理器
	if n.alertManager == nil {
		n.alertManager = NewDefaultAlertManager(n.logger)
	}
	
	return nil
}

// OnMsg 处理消息
func (n *AlertNode) OnMsg(ctx types.RuleContext, msg types.RuleMsg) {
	startTime := time.Now()
	
	// 解析告警配置
	var alertConfig AlertConfig
	if err := json.Unmarshal([]byte(ctx.GetNodeConfig().Configuration), &alertConfig); err != nil {
		n.logger.Error("解析告警配置失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 解析消息数据
	var msgData map[string]interface{}
	if err := json.Unmarshal([]byte(msg.Data), &msgData); err != nil {
		n.logger.Error("解析消息数据失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 检查告警条件
	if !n.shouldAlert(msgData, alertConfig) {
		n.logger.Debug("不满足告警条件，跳过告警")
		ctx.TellSuccess(msg)
		return
	}
	
	// 构造告警信息
	alert := n.buildAlert(msgData, alertConfig, msg)
	
	// 发送告警
	alertCtx := context.Background()
	if err := n.alertManager.SendAlert(alertCtx, alert); err != nil {
		n.logger.Error("发送告警失败",
			zap.String("alert_id", alert.ID),
			zap.String("level", string(alert.Level)),
			zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 构造输出消息
	outputMsg := msg.Copy()
	
	// 设置告警结果到消息数据
	alertResult := map[string]interface{}{
		"alert_sent":    true,
		"alert_id":      alert.ID,
		"alert_level":   alert.Level,
		"alert_message": alert.Message,
		"channels":      alert.Channels,
		"sent_at":       time.Now(),
		"duration_ms":   time.Since(startTime).Milliseconds(),
		"original_data": msgData,
	}
	
	resultBytes, err := json.Marshal(alertResult)
	if err != nil {
		n.logger.Error("序列化告警结果失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	outputMsg.Data = string(resultBytes)
	outputMsg.Type = "alert_result"
	
	// 设置元数据
	outputMsg.Metadata.PutValue("alert_id", alert.ID)
	outputMsg.Metadata.PutValue("alert_level", string(alert.Level))
	outputMsg.Metadata.PutValue("alert_sent", true)
	
	ctx.TellSuccess(outputMsg)
	
	n.logger.Info("告警发送成功",
		zap.String("alert_id", alert.ID),
		zap.String("level", string(alert.Level)),
		zap.String("message", alert.Message),
		zap.Strings("channels", alert.Channels),
		zap.Duration("duration", time.Since(startTime)))
}

// Destroy 销毁节点
func (n *AlertNode) Destroy() {
	// 清理资源
}

// shouldAlert 检查是否应该发送告警
func (n *AlertNode) shouldAlert(data map[string]interface{}, config AlertConfig) bool {
	// 检查告警条件
	if config.Conditions != nil {
		for key, expectedValue := range config.Conditions {
			if actualValue, exists := data[key]; !exists || actualValue != expectedValue {
				return false
			}
		}
	}
	
	// 检查分析结果
	if analysis, ok := data["analysis"].(map[string]interface{}); ok {
		// 如果有异常检测结果
		if anomaly, ok := analysis["anomaly"].(bool); ok && anomaly {
			return true
		}
		
		// 检查严重程度
		if severity, ok := analysis["severity"].(string); ok {
			switch severity {
			case "warning", "error", "critical":
				return true
			}
		}
		
		// 检查状态
		if status, ok := analysis["status"].(string); ok {
			switch status {
			case "warning", "error", "anomaly_detected":
				return true
			}
		}
	}
	
	return false
}

// buildAlert 构造告警信息
func (n *AlertNode) buildAlert(data map[string]interface{}, config AlertConfig, msg types.RuleMsg) Alert {
	alert := Alert{
		ID:        fmt.Sprintf("alert_%d", time.Now().UnixNano()),
		Level:     config.Level,
		Title:     config.Title,
		Message:   config.Message,
		Source:    "aiops_rulego",
		Channels:  config.Channels,
		Data:      data,
		Timestamp: time.Now(),
		Tags:      config.Tags,
	}
	
	// 从消息元数据获取指标信息
	if metricName, ok := msg.Metadata.GetValue("metric_name"); ok {
		alert.MetricName = metricName.(string)
	}
	
	// 从分析结果获取指标值
	if analysis, ok := data["analysis"].(map[string]interface{}); ok {
		if details, ok := analysis["details"].(map[string]interface{}); ok {
			if value, ok := details["current_value"].(float64); ok {
				alert.MetricValue = value
			}
		}
	}
	
	// 模板替换
	if config.Template != "" {
		alert.Message = n.renderTemplate(config.Template, data, msg)
	}
	
	// 如果没有指定渠道，使用默认渠道
	if len(alert.Channels) == 0 {
		alert.Channels = []string{"default"}
	}
	
	return alert
}

// renderTemplate 渲染消息模板
func (n *AlertNode) renderTemplate(template string, data map[string]interface{}, msg types.RuleMsg) string {
	// 简单的模板替换实现
	// 实际应用中可以使用更复杂的模板引擎
	
	message := template
	
	// 替换指标名称
	if metricName, ok := msg.Metadata.GetValue("metric_name"); ok {
		message = fmt.Sprintf(message, metricName)
	}
	
	// 替换指标值
	if analysis, ok := data["analysis"].(map[string]interface{}); ok {
		if details, ok := analysis["details"].(map[string]interface{}); ok {
			if value, ok := details["current_value"].(float64); ok {
				message = fmt.Sprintf(message, value)
			}
		}
	}
	
	return message
}

// DefaultAlertManager 默认告警管理器实现
type DefaultAlertManager struct {
	logger *zap.Logger
}

// NewDefaultAlertManager 创建默认告警管理器
func NewDefaultAlertManager(logger *zap.Logger) *DefaultAlertManager {
	return &DefaultAlertManager{
		logger: logger,
	}
}

// SendAlert 发送告警
func (am *DefaultAlertManager) SendAlert(ctx context.Context, alert Alert) error {
	// 这里实现具体的告警发送逻辑
	// 可以集成邮件、短信、Slack、钉钉等告警渠道
	
	am.logger.Info("发送告警",
		zap.String("alert_id", alert.ID),
		zap.String("level", string(alert.Level)),
		zap.String("title", alert.Title),
		zap.String("message", alert.Message),
		zap.Strings("channels", alert.Channels))
	
	// 模拟发送过程
	for _, channel := range alert.Channels {
		if err := am.sendToChannel(ctx, alert, channel); err != nil {
			return fmt.Errorf("发送到渠道 %s 失败: %w", channel, err)
		}
	}
	
	return nil
}

// sendToChannel 发送到指定渠道
func (am *DefaultAlertManager) sendToChannel(ctx context.Context, alert Alert, channel string) error {
	switch channel {
	case "email":
		return am.sendEmail(ctx, alert)
	case "slack":
		return am.sendSlack(ctx, alert)
	case "webhook":
		return am.sendWebhook(ctx, alert)
	default:
		am.logger.Info("发送告警到默认渠道",
			zap.String("channel", channel),
			zap.String("alert_id", alert.ID))
		return nil
	}
}

// sendEmail 发送邮件告警
func (am *DefaultAlertManager) sendEmail(ctx context.Context, alert Alert) error {
	am.logger.Info("发送邮件告警", zap.String("alert_id", alert.ID))
	// 实现邮件发送逻辑
	return nil
}

// sendSlack 发送 Slack 告警
func (am *DefaultAlertManager) sendSlack(ctx context.Context, alert Alert) error {
	am.logger.Info("发送 Slack 告警", zap.String("alert_id", alert.ID))
	// 实现 Slack 发送逻辑
	return nil
}

// sendWebhook 发送 Webhook 告警
func (am *DefaultAlertManager) sendWebhook(ctx context.Context, alert Alert) error {
	am.logger.Info("发送 Webhook 告警", zap.String("alert_id", alert.ID))
	// 实现 Webhook 发送逻辑
	return nil
}
