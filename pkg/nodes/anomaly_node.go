// Package nodes 异常检测节点实现
package nodes

import (
	"encoding/json"
	"fmt"
	"math"
	"time"

	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// AnomalyDetectionNode 异常检测节点
type AnomalyDetectionNode struct {
	logger *zap.Logger
}

// AnomalyConfig 异常检测配置
type AnomalyConfig struct {
	Algorithm    string                 `json:"algorithm"`    // zscore, iqr, isolation_forest
	WindowSize   int                    `json:"window_size"`  // 历史数据窗口大小
	Sensitivity  float64                `json:"sensitivity"`  // 敏感度 (0-1)
	Threshold    float64                `json:"threshold"`    // 异常阈值
	MetricName   string                 `json:"metric_name"`
	Parameters   map[string]interface{} `json:"parameters,omitempty"`
}

// AnomalyResult 异常检测结果
type AnomalyResult struct {
	IsAnomaly     bool                   `json:"is_anomaly"`
	Score         float64                `json:"score"`         // 异常分数
	Confidence    float64                `json:"confidence"`    // 置信度
	Algorithm     string                 `json:"algorithm"`
	Threshold     float64                `json:"threshold"`
	CurrentValue  float64                `json:"current_value"`
	ExpectedRange [2]float64             `json:"expected_range,omitempty"`
	Message       string                 `json:"message"`
	Details       map[string]interface{} `json:"details"`
	DetectedAt    time.Time              `json:"detected_at"`
}

// Type 返回节点类型
func (n *AnomalyDetectionNode) Type() string {
	return "aiops/anomaly"
}

// New 创建新的节点实例
func (n *AnomalyDetectionNode) New() types.Node {
	return &AnomalyDetectionNode{
		logger: n.logger,
	}
}

// Init 初始化节点
func (n *AnomalyDetectionNode) Init(ruleConfig types.Config, configuration types.Configuration) error {
	return nil
}

// OnMsg 处理消息
func (n *AnomalyDetectionNode) OnMsg(ctx types.RuleContext, msg types.RuleMsg) {
	startTime := time.Now()
	
	// 解析异常检测配置
	var anomalyConfig AnomalyConfig
	if err := json.Unmarshal([]byte(ctx.GetNodeConfig().Configuration), &anomalyConfig); err != nil {
		n.logger.Error("解析异常检测配置失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 解析消息数据
	var msgData map[string]interface{}
	if err := json.Unmarshal([]byte(msg.Data), &msgData); err != nil {
		n.logger.Error("解析消息数据失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 执行异常检测
	result := n.detectAnomaly(msgData, anomalyConfig, msg)
	
	// 构造输出消息
	outputMsg := msg.Copy()
	
	// 设置异常检测结果到消息数据
	anomalyResult := map[string]interface{}{
		"anomaly_detection": result,
		"processed_at":      time.Now(),
		"duration_ms":       time.Since(startTime).Milliseconds(),
		"original_data":     msgData,
	}
	
	resultBytes, err := json.Marshal(anomalyResult)
	if err != nil {
		n.logger.Error("序列化异常检测结果失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	outputMsg.Data = string(resultBytes)
	outputMsg.Type = "anomaly_result"
	
	// 设置元数据
	outputMsg.Metadata.PutValue("is_anomaly", result.IsAnomaly)
	outputMsg.Metadata.PutValue("anomaly_score", result.Score)
	outputMsg.Metadata.PutValue("anomaly_confidence", result.Confidence)
	outputMsg.Metadata.PutValue("algorithm", result.Algorithm)
	
	// 根据检测结果选择输出链路
	if result.IsAnomaly {
		if result.Confidence > 0.8 {
			ctx.TellNext(outputMsg, "high_confidence_anomaly")
		} else {
			ctx.TellNext(outputMsg, "anomaly")
		}
	} else {
		ctx.TellSuccess(outputMsg)
	}
	
	n.logger.Debug("异常检测完成",
		zap.String("metric_name", anomalyConfig.MetricName),
		zap.Bool("is_anomaly", result.IsAnomaly),
		zap.Float64("score", result.Score),
		zap.Float64("confidence", result.Confidence),
		zap.String("algorithm", result.Algorithm),
		zap.Duration("duration", time.Since(startTime)))
}

// Destroy 销毁节点
func (n *AnomalyDetectionNode) Destroy() {
	// 清理资源
}

// detectAnomaly 执行异常检测
func (n *AnomalyDetectionNode) detectAnomaly(data map[string]interface{}, config AnomalyConfig, msg types.RuleMsg) *AnomalyResult {
	result := &AnomalyResult{
		IsAnomaly:  false,
		Score:      0.0,
		Confidence: 0.0,
		Algorithm:  config.Algorithm,
		Threshold:  config.Threshold,
		Message:    "正常",
		Details:    make(map[string]interface{}),
		DetectedAt: time.Now(),
	}
	
	// 提取当前值
	value, found := n.extractMetricValue(data, config.MetricName)
	if !found {
		result.Message = fmt.Sprintf("未找到指标 %s 的值", config.MetricName)
		return result
	}
	
	result.CurrentValue = value
	
	// 根据算法执行异常检测
	switch config.Algorithm {
	case "zscore":
		return n.detectZScoreAnomaly(value, config, result)
	case "iqr":
		return n.detectIQRAnomaly(value, config, result)
	case "threshold":
		return n.detectThresholdAnomaly(value, config, result)
	default:
		return n.detectSimpleAnomaly(value, config, result)
	}
}

// detectZScoreAnomaly Z-Score 异常检测
func (n *AnomalyDetectionNode) detectZScoreAnomaly(value float64, config AnomalyConfig, result *AnomalyResult) *AnomalyResult {
	// 这里应该使用历史数据计算均值和标准差
	// 为了演示，我们使用模拟的统计数据
	
	// 模拟历史数据统计
	mean := 50.0  // 假设均值
	stdDev := 10.0 // 假设标准差
	
	// 从配置中获取统计参数
	if params, ok := config.Parameters["statistics"].(map[string]interface{}); ok {
		if m, ok := params["mean"].(float64); ok {
			mean = m
		}
		if s, ok := params["std_dev"].(float64); ok {
			stdDev = s
		}
	}
	
	// 计算 Z-Score
	zScore := math.Abs(value - mean) / stdDev
	
	// 设置阈值（默认为2.0，表示2个标准差）
	threshold := config.Threshold
	if threshold == 0 {
		threshold = 2.0
	}
	
	result.Score = zScore
	result.Details["z_score"] = zScore
	result.Details["mean"] = mean
	result.Details["std_dev"] = stdDev
	result.Details["threshold"] = threshold
	
	// 判断是否异常
	if zScore > threshold {
		result.IsAnomaly = true
		result.Confidence = math.Min(zScore/threshold, 1.0)
		result.Message = fmt.Sprintf("Z-Score异常: %.2f (阈值: %.2f)", zScore, threshold)
		
		// 计算期望范围
		result.ExpectedRange = [2]float64{
			mean - threshold*stdDev,
			mean + threshold*stdDev,
		}
	} else {
		result.Confidence = 1.0 - (zScore / threshold)
		result.Message = "Z-Score正常"
	}
	
	return result
}

// detectIQRAnomaly IQR (四分位距) 异常检测
func (n *AnomalyDetectionNode) detectIQRAnomaly(value float64, config AnomalyConfig, result *AnomalyResult) *AnomalyResult {
	// 这里应该使用历史数据计算四分位数
	// 为了演示，我们使用模拟的四分位数
	
	q1 := 30.0 // 第一四分位数
	q3 := 70.0 // 第三四分位数
	
	// 从配置中获取四分位数参数
	if params, ok := config.Parameters["quartiles"].(map[string]interface{}); ok {
		if q, ok := params["q1"].(float64); ok {
			q1 = q
		}
		if q, ok := params["q3"].(float64); ok {
			q3 = q
		}
	}
	
	iqr := q3 - q1
	multiplier := config.Sensitivity
	if multiplier == 0 {
		multiplier = 1.5 // 默认1.5倍IQR
	}
	
	lowerBound := q1 - multiplier*iqr
	upperBound := q3 + multiplier*iqr
	
	result.Details["q1"] = q1
	result.Details["q3"] = q3
	result.Details["iqr"] = iqr
	result.Details["lower_bound"] = lowerBound
	result.Details["upper_bound"] = upperBound
	result.Details["multiplier"] = multiplier
	
	// 判断是否异常
	if value < lowerBound || value > upperBound {
		result.IsAnomaly = true
		result.Message = fmt.Sprintf("IQR异常: %.2f 超出范围 [%.2f, %.2f]", value, lowerBound, upperBound)
		
		// 计算异常分数和置信度
		if value < lowerBound {
			result.Score = (lowerBound - value) / iqr
			result.Details["anomaly_type"] = "below_lower_bound"
		} else {
			result.Score = (value - upperBound) / iqr
			result.Details["anomaly_type"] = "above_upper_bound"
		}
		
		result.Confidence = math.Min(result.Score, 1.0)
		result.ExpectedRange = [2]float64{lowerBound, upperBound}
	} else {
		result.Message = "IQR正常"
		// 计算在正常范围内的置信度
		distanceFromBounds := math.Min(value-lowerBound, upperBound-value)
		result.Confidence = distanceFromBounds / (iqr * multiplier)
	}
	
	return result
}

// detectThresholdAnomaly 阈值异常检测
func (n *AnomalyDetectionNode) detectThresholdAnomaly(value float64, config AnomalyConfig, result *AnomalyResult) *AnomalyResult {
	threshold := config.Threshold
	if threshold == 0 {
		threshold = 80.0 // 默认阈值
	}
	
	result.Details["threshold"] = threshold
	
	if value > threshold {
		result.IsAnomaly = true
		result.Score = (value - threshold) / threshold
		result.Confidence = math.Min(result.Score, 1.0)
		result.Message = fmt.Sprintf("阈值异常: %.2f > %.2f", value, threshold)
		result.ExpectedRange = [2]float64{0, threshold}
	} else {
		result.Message = "阈值正常"
		result.Confidence = 1.0 - (value / threshold)
	}
	
	return result
}

// detectSimpleAnomaly 简单异常检测
func (n *AnomalyDetectionNode) detectSimpleAnomaly(value float64, config AnomalyConfig, result *AnomalyResult) *AnomalyResult {
	// 简单的基于预定义范围的异常检测
	normalRange := [2]float64{0, 100} // 默认正常范围
	
	if params, ok := config.Parameters["normal_range"].([]interface{}); ok && len(params) == 2 {
		if min, ok := params[0].(float64); ok {
			normalRange[0] = min
		}
		if max, ok := params[1].(float64); ok {
			normalRange[1] = max
		}
	}
	
	result.Details["normal_range"] = normalRange
	result.ExpectedRange = normalRange
	
	if value < normalRange[0] || value > normalRange[1] {
		result.IsAnomaly = true
		result.Message = fmt.Sprintf("简单异常: %.2f 超出正常范围 [%.2f, %.2f]", 
			value, normalRange[0], normalRange[1])
		
		// 计算异常分数
		rangeSize := normalRange[1] - normalRange[0]
		if value < normalRange[0] {
			result.Score = (normalRange[0] - value) / rangeSize
		} else {
			result.Score = (value - normalRange[1]) / rangeSize
		}
		
		result.Confidence = math.Min(result.Score, 1.0)
	} else {
		result.Message = "简单检测正常"
		// 计算在正常范围内的置信度
		distanceFromBounds := math.Min(value-normalRange[0], normalRange[1]-value)
		rangeSize := normalRange[1] - normalRange[0]
		result.Confidence = distanceFromBounds / rangeSize
	}
	
	return result
}

// extractMetricValue 提取指标值
func (n *AnomalyDetectionNode) extractMetricValue(data map[string]interface{}, metricName string) (float64, bool) {
	// 尝试从不同位置提取指标值
	
	// 1. 直接从 value 字段
	if value, ok := data["value"]; ok {
		if v, ok := value.(float64); ok {
			return v, true
		}
		if v, ok := value.(int); ok {
			return float64(v), true
		}
	}
	
	// 2. 从分析结果中提取
	if analysis, ok := data["analysis"].(map[string]interface{}); ok {
		if details, ok := analysis["details"].(map[string]interface{}); ok {
			if value, ok := details["current_value"].(float64); ok {
				return value, true
			}
		}
	}
	
	// 3. 从原始数据中提取
	if originalData, ok := data["original_data"].(map[string]interface{}); ok {
		if value, ok := originalData["value"]; ok {
			if v, ok := value.(float64); ok {
				return v, true
			}
		}
	}
	
	// 4. 从指定的指标名称字段提取
	if metricName != "" {
		if value, ok := data[metricName]; ok {
			if v, ok := value.(float64); ok {
				return v, true
			}
			if v, ok := value.(int); ok {
				return float64(v), true
			}
		}
	}
	
	return 0, false
}
