// Package nodes 日志节点实现
package nodes

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// LogNode 日志节点
type LogNode struct {
	logger *zap.Logger
}

// LogConfig 日志配置
type LogConfig struct {
	Level       string                 `json:"level"`        // debug, info, warn, error
	Message     string                 `json:"message"`      // 日志消息模板
	Fields      []string               `json:"fields"`       // 要记录的字段
	Format      string                 `json:"format"`       // json, text
	Target      string                 `json:"target"`       // console, file, remote
	Template    string                 `json:"template"`     // 消息模板
	Structured  bool                   `json:"structured"`   // 是否结构化日志
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
}

// LogResult 日志结果
type LogResult struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	Level     string                 `json:"level"`
	Fields    map[string]interface{} `json:"fields"`
	LoggedAt  time.Time              `json:"logged_at"`
	Target    string                 `json:"target"`
	Error     string                 `json:"error,omitempty"`
}

// Type 返回节点类型
func (n *LogNode) Type() string {
	return "aiops/log"
}

// New 创建新的节点实例
func (n *LogNode) New() types.Node {
	return &LogNode{
		logger: n.logger,
	}
}

// Init 初始化节点
func (n *LogNode) Init(ruleConfig types.Config, configuration types.Configuration) error {
	return nil
}

// OnMsg 处理消息
func (n *LogNode) OnMsg(ctx types.RuleContext, msg types.RuleMsg) {
	startTime := time.Now()
	
	// 解析日志配置
	var logConfig LogConfig
	if err := json.Unmarshal([]byte(ctx.GetNodeConfig().Configuration), &logConfig); err != nil {
		n.logger.Error("解析日志配置失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 解析消息数据
	var msgData map[string]interface{}
	if err := json.Unmarshal([]byte(msg.Data), &msgData); err != nil {
		n.logger.Error("解析消息数据失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 执行日志记录
	result := n.logMessage(msgData, logConfig, msg)
	
	// 构造输出消息
	outputMsg := msg.Copy()
	
	// 设置日志结果到消息数据
	logResult := map[string]interface{}{
		"log_result":    result,
		"processed_at":  time.Now(),
		"duration_ms":   time.Since(startTime).Milliseconds(),
		"original_data": msgData,
	}
	
	resultBytes, err := json.Marshal(logResult)
	if err != nil {
		n.logger.Error("序列化日志结果失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	outputMsg.Data = string(resultBytes)
	outputMsg.Type = "log_result"
	
	// 设置元数据
	outputMsg.Metadata.PutValue("log_success", result.Success)
	outputMsg.Metadata.PutValue("log_level", result.Level)
	outputMsg.Metadata.PutValue("log_target", result.Target)
	
	if result.Success {
		ctx.TellSuccess(outputMsg)
	} else {
		ctx.TellFailure(outputMsg, fmt.Errorf("日志记录失败: %s", result.Error))
	}
	
	n.logger.Debug("日志记录完成",
		zap.Bool("success", result.Success),
		zap.String("level", result.Level),
		zap.String("target", result.Target),
		zap.Duration("duration", time.Since(startTime)))
}

// Destroy 销毁节点
func (n *LogNode) Destroy() {
	// 清理资源
}

// logMessage 记录日志消息
func (n *LogNode) logMessage(data map[string]interface{}, config LogConfig, msg types.RuleMsg) *LogResult {
	result := &LogResult{
		Success:  true,
		Level:    config.Level,
		Fields:   make(map[string]interface{}),
		LoggedAt: time.Now(),
		Target:   config.Target,
	}
	
	// 设置默认值
	if result.Level == "" {
		result.Level = "info"
	}
	if result.Target == "" {
		result.Target = "console"
	}
	
	// 构建日志消息
	message := n.buildLogMessage(data, config, msg)
	result.Message = message
	
	// 提取要记录的字段
	n.extractLogFields(data, config.Fields, result.Fields)
	
	// 根据配置记录日志
	switch config.Format {
	case "json":
		n.logStructured(result, config)
	default:
		n.logText(result, config)
	}
	
	return result
}

// buildLogMessage 构建日志消息
func (n *LogNode) buildLogMessage(data map[string]interface{}, config LogConfig, msg types.RuleMsg) string {
	message := config.Message
	if message == "" {
		message = "AIOps规则链日志"
	}
	
	// 使用模板替换
	if config.Template != "" {
		message = n.renderTemplate(config.Template, data, msg)
	}
	
	return message
}

// renderTemplate 渲染消息模板
func (n *LogNode) renderTemplate(template string, data map[string]interface{}, msg types.RuleMsg) string {
	result := template
	
	// 替换消息元数据
	if metricName, ok := msg.Metadata.GetValue("metric_name"); ok {
		result = strings.ReplaceAll(result, "{{metric_name}}", fmt.Sprintf("%v", metricName))
	}
	
	if chainID, ok := msg.Metadata.GetValue("chain_id"); ok {
		result = strings.ReplaceAll(result, "{{chain_id}}", fmt.Sprintf("%v", chainID))
	}
	
	// 替换数据字段
	for key, value := range data {
		placeholder := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, placeholder, fmt.Sprintf("%v", value))
	}
	
	// 替换时间戳
	result = strings.ReplaceAll(result, "{{timestamp}}", time.Now().Format(time.RFC3339))
	
	return result
}

// extractLogFields 提取要记录的字段
func (n *LogNode) extractLogFields(data map[string]interface{}, fields []string, result map[string]interface{}) {
	if len(fields) == 0 {
		// 如果没有指定字段，记录所有字段
		for k, v := range data {
			result[k] = v
		}
		return
	}
	
	// 记录指定字段
	for _, field := range fields {
		if value := n.getNestedValue(data, field); value != nil {
			result[field] = value
		}
	}
}

// getNestedValue 获取嵌套字段值
func (n *LogNode) getNestedValue(data map[string]interface{}, path string) interface{} {
	parts := strings.Split(path, ".")
	current := data
	
	for i, part := range parts {
		if i == len(parts)-1 {
			return current[part]
		}
		
		if next, ok := current[part].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}
	
	return nil
}

// logStructured 记录结构化日志
func (n *LogNode) logStructured(result *LogResult, config LogConfig) {
	fields := []zap.Field{
		zap.String("target", result.Target),
		zap.Time("logged_at", result.LoggedAt),
		zap.Any("fields", result.Fields),
	}
	
	// 添加自定义字段
	for key, value := range result.Fields {
		fields = append(fields, zap.Any(key, value))
	}
	
	switch result.Level {
	case "debug":
		n.logger.Debug(result.Message, fields...)
	case "info":
		n.logger.Info(result.Message, fields...)
	case "warn":
		n.logger.Warn(result.Message, fields...)
	case "error":
		n.logger.Error(result.Message, fields...)
	default:
		n.logger.Info(result.Message, fields...)
	}
}

// logText 记录文本日志
func (n *LogNode) logText(result *LogResult, config LogConfig) {
	// 构建文本格式的日志消息
	var parts []string
	parts = append(parts, result.Message)
	
	// 添加字段信息
	for key, value := range result.Fields {
		parts = append(parts, fmt.Sprintf("%s=%v", key, value))
	}
	
	fullMessage := strings.Join(parts, " ")
	
	switch result.Level {
	case "debug":
		n.logger.Debug(fullMessage)
	case "info":
		n.logger.Info(fullMessage)
	case "warn":
		n.logger.Warn(fullMessage)
	case "error":
		n.logger.Error(fullMessage)
	default:
		n.logger.Info(fullMessage)
	}
}

// logToFile 记录到文件（扩展功能）
func (n *LogNode) logToFile(result *LogResult, config LogConfig) error {
	// 这里可以实现文件日志记录
	// 为了简化，暂时只记录到标准日志
	n.logger.Info("文件日志记录", 
		zap.String("message", result.Message),
		zap.Any("fields", result.Fields))
	return nil
}

// logToRemote 记录到远程（扩展功能）
func (n *LogNode) logToRemote(result *LogResult, config LogConfig) error {
	// 这里可以实现远程日志记录，如发送到日志收集系统
	// 为了简化，暂时只记录到标准日志
	n.logger.Info("远程日志记录", 
		zap.String("message", result.Message),
		zap.Any("fields", result.Fields))
	return nil
}
