// Package nodes AIOps 专用节点实现
package nodes

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// AnalysisNode 数据分析节点
type AnalysisNode struct {
	analyzer DataAnalyzer
	logger   *zap.Logger
}

// DataAnalyzer 数据分析器接口
type DataAnalyzer interface {
	Analyze(ctx context.Context, data MetricData) (*AnalysisResult, error)
}

// MetricData 指标数据
type MetricData struct {
	MetricName string                 `json:"metric_name"`
	Value      float64                `json:"value"`
	Timestamp  time.Time              `json:"timestamp"`
	Labels     map[string]string      `json:"labels,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	Type      string                 `json:"type"`      // 分析类型
	Algorithm string                 `json:"algorithm"` // 使用的算法
	Status    string                 `json:"status"`    // 分析状态
	Message   string                 `json:"message"`   // 分析结果描述
	Severity  string                 `json:"severity"`  // 严重程度
	Details   map[string]interface{} `json:"details"`   // 详细分析数据
	Anomaly   bool                   `json:"anomaly"`   // 是否异常
	Trend     string                 `json:"trend"`     // 趋势
	Score     float64                `json:"score"`     // 分数
	Timestamp time.Time              `json:"timestamp"` // 分析时间
}

// AnalysisConfig 分析配置
type AnalysisConfig struct {
	AnalysisType string                 `json:"analysis_type"` // 分析类型: trend, anomaly, threshold
	WindowSize   string                 `json:"window_size"`   // 时间窗口
	Algorithms   []string               `json:"algorithms"`    // 使用的算法
	Thresholds   map[string]float64     `json:"thresholds"`    // 阈值配置
	Parameters   map[string]interface{} `json:"parameters"`    // 其他参数
}

// Type 返回节点类型
func (n *AnalysisNode) Type() string {
	return "aiops/analysis"
}

// New 创建新的节点实例
func (n *AnalysisNode) New() types.Node {
	return &AnalysisNode{
		analyzer: n.analyzer,
		logger:   n.logger,
	}
}

// Init 初始化节点
func (n *AnalysisNode) Init(ruleConfig types.Config, configuration types.Configuration) error {
	// 解析配置
	var config AnalysisConfig
	if err := json.Unmarshal([]byte(configuration.Properties), &config); err != nil {
		return fmt.Errorf("解析分析节点配置失败: %w", err)
	}
	
	// 初始化分析器
	if n.analyzer == nil {
		n.analyzer = NewDefaultAnalyzer(config, n.logger)
	}
	
	return nil
}

// OnMsg 处理消息
func (n *AnalysisNode) OnMsg(ctx types.RuleContext, msg types.RuleMsg) {
	startTime := time.Now()
	
	// 解析输入数据
	var metrics MetricData
	if err := json.Unmarshal([]byte(msg.Data), &metrics); err != nil {
		n.logger.Error("解析指标数据失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 执行数据分析
	analysisCtx := context.Background()
	result, err := n.analyzer.Analyze(analysisCtx, metrics)
	if err != nil {
		n.logger.Error("数据分析失败", 
			zap.String("metric_name", metrics.MetricName),
			zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	// 构造输出消息
	outputMsg := msg.Copy()
	
	// 设置分析结果到消息数据
	resultData := map[string]interface{}{
		"original_data": metrics,
		"analysis":      result,
		"processed_at":  time.Now(),
		"duration_ms":   time.Since(startTime).Milliseconds(),
	}
	
	resultBytes, err := json.Marshal(resultData)
	if err != nil {
		n.logger.Error("序列化分析结果失败", zap.Error(err))
		ctx.TellFailure(msg, err)
		return
	}
	
	outputMsg.Data = string(resultBytes)
	outputMsg.Type = "analysis_result"
	
	// 设置元数据
	outputMsg.Metadata.PutValue("analysis_type", result.Type)
	outputMsg.Metadata.PutValue("analysis_status", result.Status)
	outputMsg.Metadata.PutValue("analysis_severity", result.Severity)
	outputMsg.Metadata.PutValue("analysis_anomaly", result.Anomaly)
	outputMsg.Metadata.PutValue("analysis_score", result.Score)
	outputMsg.Metadata.PutValue("metric_name", metrics.MetricName)
	
	// 根据分析结果选择输出链路
	if result.Anomaly || result.Severity == "critical" || result.Severity == "warning" {
		// 异常情况，发送到告警链路
		ctx.TellNext(outputMsg, "alert")
	} else {
		// 正常情况，发送到下一个节点
		ctx.TellSuccess(outputMsg)
	}
	
	n.logger.Debug("数据分析完成",
		zap.String("metric_name", metrics.MetricName),
		zap.String("analysis_type", result.Type),
		zap.String("status", result.Status),
		zap.Bool("anomaly", result.Anomaly),
		zap.Duration("duration", time.Since(startTime)))
}

// Destroy 销毁节点
func (n *AnalysisNode) Destroy() {
	// 清理资源
}

// DefaultAnalyzer 默认分析器实现
type DefaultAnalyzer struct {
	config AnalysisConfig
	logger *zap.Logger
}

// NewDefaultAnalyzer 创建默认分析器
func NewDefaultAnalyzer(config AnalysisConfig, logger *zap.Logger) *DefaultAnalyzer {
	return &DefaultAnalyzer{
		config: config,
		logger: logger,
	}
}

// Analyze 执行分析
func (a *DefaultAnalyzer) Analyze(ctx context.Context, data MetricData) (*AnalysisResult, error) {
	result := &AnalysisResult{
		Type:      a.config.AnalysisType,
		Algorithm: "default",
		Status:    "normal",
		Message:   "分析完成",
		Severity:  "info",
		Details:   make(map[string]interface{}),
		Anomaly:   false,
		Trend:     "stable",
		Score:     0.0,
		Timestamp: time.Now(),
	}
	
	// 根据分析类型执行不同的分析逻辑
	switch a.config.AnalysisType {
	case "threshold":
		return a.analyzeThreshold(data, result)
	case "anomaly":
		return a.analyzeAnomaly(data, result)
	case "trend":
		return a.analyzeTrend(data, result)
	default:
		return a.analyzeBasic(data, result)
	}
}

// analyzeThreshold 阈值分析
func (a *DefaultAnalyzer) analyzeThreshold(data MetricData, result *AnalysisResult) (*AnalysisResult, error) {
	result.Algorithm = "threshold"
	
	// 检查阈值配置
	if thresholds, ok := a.config.Thresholds[data.MetricName]; ok {
		if data.Value > thresholds {
			result.Status = "warning"
			result.Severity = "warning"
			result.Anomaly = true
			result.Message = fmt.Sprintf("指标 %s 值 %.2f 超过阈值 %.2f", data.MetricName, data.Value, thresholds)
			result.Score = (data.Value - thresholds) / thresholds * 100
		}
	}
	
	result.Details["threshold_value"] = a.config.Thresholds[data.MetricName]
	result.Details["current_value"] = data.Value
	
	return result, nil
}

// analyzeAnomaly 异常检测分析
func (a *DefaultAnalyzer) analyzeAnomaly(data MetricData, result *AnalysisResult) (*AnalysisResult, error) {
	result.Algorithm = "anomaly_detection"
	
	// 简单的异常检测逻辑（实际应用中需要更复杂的算法）
	// 这里只是示例，可以集成机器学习模型
	
	// 假设正常值范围
	normalRange := map[string][2]float64{
		"cpu_usage":    {0, 80},
		"memory_usage": {0, 85},
		"disk_usage":   {0, 90},
	}
	
	if ranges, ok := normalRange[data.MetricName]; ok {
		if data.Value < ranges[0] || data.Value > ranges[1] {
			result.Status = "anomaly_detected"
			result.Severity = "warning"
			result.Anomaly = true
			result.Message = fmt.Sprintf("检测到异常: %s 值 %.2f 超出正常范围 [%.2f, %.2f]", 
				data.MetricName, data.Value, ranges[0], ranges[1])
			
			// 计算异常分数
			if data.Value > ranges[1] {
				result.Score = (data.Value - ranges[1]) / ranges[1] * 100
			} else {
				result.Score = (ranges[0] - data.Value) / ranges[0] * 100
			}
		}
	}
	
	result.Details["normal_range"] = normalRange[data.MetricName]
	result.Details["current_value"] = data.Value
	
	return result, nil
}

// analyzeTrend 趋势分析
func (a *DefaultAnalyzer) analyzeTrend(data MetricData, result *AnalysisResult) (*AnalysisResult, error) {
	result.Algorithm = "trend_analysis"
	
	// 简单的趋势分析（实际应用中需要历史数据）
	// 这里只是示例
	
	result.Trend = "stable"
	result.Details["trend_direction"] = "stable"
	result.Details["current_value"] = data.Value
	
	return result, nil
}

// analyzeBasic 基础分析
func (a *DefaultAnalyzer) analyzeBasic(data MetricData, result *AnalysisResult) (*AnalysisResult, error) {
	result.Algorithm = "basic"
	result.Details["current_value"] = data.Value
	result.Details["metric_name"] = data.MetricName
	
	return result, nil
}
