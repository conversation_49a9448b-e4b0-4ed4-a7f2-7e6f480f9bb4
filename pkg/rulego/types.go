// Package rulego 提供 RuleGo 集成封装
package rulego

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/rulego/rulego/api/types"
)

// MessageType 消息类型
type MessageType string

const (
	MetricMessage    MessageType = "metric"
	AnalysisMessage  MessageType = "analysis"
	AlertMessage     MessageType = "alert"
	ActionMessage    MessageType = "action"
	ThresholdMessage MessageType = "threshold"
	AnomalyMessage   MessageType = "anomaly"
)

// AIOpsMessage AIOps 特定的消息类型
type AIOpsMessage struct {
	Type      MessageType            `json:"type"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
	Data      map[string]interface{} `json:"data"`
	Context   map[string]interface{} `json:"context"`
	Analysis  *AnalysisResult        `json:"analysis,omitempty"`
	Metadata  map[string]string      `json:"metadata,omitempty"`
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	Type      string                 `json:"type"`      // 分析类型
	Algorithm string                 `json:"algorithm"` // 使用的算法
	Status    string                 `json:"status"`    // 分析状态
	Message   string                 `json:"message"`   // 分析结果描述
	Severity  string                 `json:"severity"`  // 严重程度
	Details   map[string]interface{} `json:"details"`   // 详细分析数据
	Anomaly   bool                   `json:"anomaly"`   // 是否异常
	Trend     string                 `json:"trend"`     // 趋势
	Score     float64                `json:"score"`     // 分数
}

// ToRuleGoMsg 转换为 RuleGo 消息格式
func (m *AIOpsMessage) ToRuleGoMsg() types.RuleMsg {
	// 序列化数据
	dataBytes, _ := json.Marshal(m.Data)

	// 创建元数据
	metadata := types.NewMetadata()
	if m.Metadata != nil {
		for k, v := range m.Metadata {
			metadata.PutValue(k, v)
		}
	}

	// 添加时间戳和源信息
	metadata.PutValue("timestamp", fmt.Sprintf("%d", m.Timestamp.Unix()))
	metadata.PutValue("source", m.Source)

	// 添加分析结果
	if m.Analysis != nil {
		metadata.PutValue("analysis_type", m.Analysis.Type)
		metadata.PutValue("analysis_status", m.Analysis.Status)
		metadata.PutValue("analysis_severity", m.Analysis.Severity)
		metadata.PutValue("analysis_anomaly", fmt.Sprintf("%t", m.Analysis.Anomaly))
		metadata.PutValue("analysis_score", fmt.Sprintf("%.2f", m.Analysis.Score))
	}

	// 创建消息
	msg := types.NewMsg(0, string(m.Type), types.JSON, metadata, string(dataBytes))

	return msg
}

// FromRuleGoMsg 从 RuleGo 消息创建 AIOpsMessage
func FromRuleGoMsg(msg types.RuleMsg) *AIOpsMessage {
	aMsg := &AIOpsMessage{
		Type:     MessageType(msg.Type),
		Context:  make(map[string]interface{}),
		Metadata: make(map[string]string),
	}

	// 解析数据
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(msg.Data), &data); err == nil {
		aMsg.Data = data
	}

	// 解析元数据
	if timestamp := msg.Metadata.GetValue("timestamp"); timestamp != "" {
		if ts, err := strconv.ParseInt(timestamp, 10, 64); err == nil {
			aMsg.Timestamp = time.Unix(ts, 0)
		}
	}

	if source := msg.Metadata.GetValue("source"); source != "" {
		aMsg.Source = source
	}

	// 解析分析结果
	if analysisType := msg.Metadata.GetValue("analysis_type"); analysisType != "" {
		aMsg.Analysis = &AnalysisResult{
			Type: analysisType,
		}

		if status := msg.Metadata.GetValue("analysis_status"); status != "" {
			aMsg.Analysis.Status = status
		}
		if severity := msg.Metadata.GetValue("analysis_severity"); severity != "" {
			aMsg.Analysis.Severity = severity
		}
		if anomaly := msg.Metadata.GetValue("analysis_anomaly"); anomaly != "" {
			if b, err := strconv.ParseBool(anomaly); err == nil {
				aMsg.Analysis.Anomaly = b
			}
		}
		if score := msg.Metadata.GetValue("analysis_score"); score != "" {
			if f, err := strconv.ParseFloat(score, 64); err == nil {
				aMsg.Analysis.Score = f
			}
		}
	}

	return aMsg
}

// ChainDefinition 规则链定义
type ChainDefinition struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description,omitempty"`
	Version     string                 `json:"version,omitempty"`
	RuleChain   map[string]interface{} `json:"ruleChain"`
}

// NodeConfig 节点配置
type NodeConfig struct {
	Type          string                 `json:"type"`
	Name          string                 `json:"name"`
	Configuration map[string]interface{} `json:"configuration"`
}

// ExecutionContext 执行上下文
type ExecutionContext struct {
	ChainID   string
	MessageID string
	StartTime time.Time
	Metadata  map[string]interface{}
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	Success  bool                   `json:"success"`
	Message  string                 `json:"message,omitempty"`
	Error    error                  `json:"error,omitempty"`
	Duration time.Duration          `json:"duration"`
	Output   map[string]interface{} `json:"output,omitempty"`
	Context  *ExecutionContext      `json:"context,omitempty"`
}

// ChainStore 规则链存储接口
type ChainStore interface {
	GetChain(chainID string) (*ChainDefinition, error)
	SaveChain(chain *ChainDefinition) error
	DeleteChain(chainID string) error
	ListChains() ([]*ChainDefinition, error)
	GetChainsByType(chainType string) ([]*ChainDefinition, error)
}

// ExecutionMonitor 执行监控接口
type ExecutionMonitor interface {
	RecordExecution(ctx *ExecutionContext, result *ExecutionResult)
	GetExecutionStats(chainID string) (*ExecutionStats, error)
	GetNodeStats(chainID, nodeID string) (*NodeStats, error)
}

// ExecutionStats 执行统计
type ExecutionStats struct {
	ChainID         string        `json:"chain_id"`
	TotalExecutions int64         `json:"total_executions"`
	SuccessCount    int64         `json:"success_count"`
	FailureCount    int64         `json:"failure_count"`
	AvgDuration     time.Duration `json:"avg_duration"`
	LastExecution   time.Time     `json:"last_execution"`
}

// NodeStats 节点统计
type NodeStats struct {
	ChainID        string        `json:"chain_id"`
	NodeID         string        `json:"node_id"`
	NodeType       string        `json:"node_type"`
	ExecutionCount int64         `json:"execution_count"`
	SuccessCount   int64         `json:"success_count"`
	FailureCount   int64         `json:"failure_count"`
	AvgDuration    time.Duration `json:"avg_duration"`
	LastExecution  time.Time     `json:"last_execution"`
}

// Config RuleGo 引擎配置
type Config struct {
	// 引擎配置
	PoolSize     int  `json:"pool_size"`
	Debug        bool `json:"debug"`
	EnableMetric bool `json:"enable_metric"`

	// 数据库配置
	Database DatabaseConfig `json:"database"`

	// 监控配置
	Monitor MonitorConfig `json:"monitor"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver string `json:"driver"`
	DSN    string `json:"dsn"`
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	Enabled         bool          `json:"enabled"`
	MetricsInterval time.Duration `json:"metrics_interval"`
	StatsRetention  time.Duration `json:"stats_retention"`
}
