// Package rulego 节点类型定义和引用
package rulego

import (
	"aiops/pkg/nodes"
	"github.com/rulego/rulego/api/types"
)

// 节点类型别名，用于在 node_manager.go 中引用
type (
	AnalysisNode         = nodes.AnalysisNode
	AlertNode            = nodes.AlertNode
	AutoScaleNode        = nodes.AutoScaleNode
	MetricFilterNode     = nodes.MetricFilterNode
	ThresholdNode        = nodes.ThresholdNode
	AnomalyDetectionNode = nodes.AnomalyDetectionNode
	DataTransformNode    = nodes.DataTransformNode
	LogNode              = nodes.LogNode
)

// 创建节点实例的工厂函数
func NewAnalysisNode() types.Node {
	return &AnalysisNode{}
}

func NewAlertNode() types.Node {
	return &AlertNode{}
}

func NewAutoScaleNode() types.Node {
	return &AutoScaleNode{}
}

func NewMetricFilterNode() types.Node {
	return &MetricFilterNode{}
}

func NewThresholdNode() types.Node {
	return &ThresholdNode{}
}

func NewAnomalyDetectionNode() types.Node {
	return &AnomalyDetectionNode{}
}

func NewDataTransformNode() types.Node {
	return &DataTransformNode{}
}

func NewLogNode() types.Node {
	return &LogNode{}
}
