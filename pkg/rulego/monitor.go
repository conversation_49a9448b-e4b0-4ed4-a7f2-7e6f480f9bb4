// Package rulego 执行监控实现
package rulego

import (
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// DefaultExecutionMonitor 默认执行监控器
type DefaultExecutionMonitor struct {
	config MonitorConfig
	logger *zap.Logger
	
	// 统计数据存储
	chainStats map[string]*ExecutionStats
	nodeStats  map[string]*NodeStats
	mu         sync.RWMutex
	
	// 清理定时器
	cleanupTicker *time.Ticker
	stopChan      chan struct{}
}

// NewExecutionMonitor 创建执行监控器
func NewExecutionMonitor(config MonitorConfig, logger *zap.Logger) *DefaultExecutionMonitor {
	monitor := &DefaultExecutionMonitor{
		config:     config,
		logger:     logger,
		chainStats: make(map[string]*ExecutionStats),
		nodeStats:  make(map[string]*NodeStats),
		stopChan:   make(chan struct{}),
	}
	
	// 启动清理定时器
	if config.StatsRetention > 0 {
		monitor.cleanupTicker = time.NewTicker(config.StatsRetention / 10) // 每1/10保留期清理一次
		go monitor.cleanupLoop()
	}
	
	return monitor
}

// RecordExecution 记录执行统计
func (m *DefaultExecutionMonitor) RecordExecution(ctx *ExecutionContext, result *ExecutionResult) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// 更新规则链统计
	chainStats, exists := m.chainStats[ctx.ChainID]
	if !exists {
		chainStats = &ExecutionStats{
			ChainID: ctx.ChainID,
		}
		m.chainStats[ctx.ChainID] = chainStats
	}
	
	chainStats.TotalExecutions++
	if result.Success {
		chainStats.SuccessCount++
	} else {
		chainStats.FailureCount++
	}
	
	// 更新平均执行时间
	if chainStats.TotalExecutions == 1 {
		chainStats.AvgDuration = result.Duration
	} else {
		// 使用移动平均
		chainStats.AvgDuration = time.Duration(
			(int64(chainStats.AvgDuration)*int64(chainStats.TotalExecutions-1) + int64(result.Duration)) / int64(chainStats.TotalExecutions),
		)
	}
	
	chainStats.LastExecution = time.Now()
	
	m.logger.Debug("记录执行统计",
		zap.String("chain_id", ctx.ChainID),
		zap.Bool("success", result.Success),
		zap.Duration("duration", result.Duration),
		zap.Int64("total_executions", chainStats.TotalExecutions))
}

// GetExecutionStats 获取执行统计
func (m *DefaultExecutionMonitor) GetExecutionStats(chainID string) (*ExecutionStats, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	stats, exists := m.chainStats[chainID]
	if !exists {
		return nil, fmt.Errorf("规则链统计不存在: %s", chainID)
	}
	
	// 返回副本，避免并发修改
	return &ExecutionStats{
		ChainID:         stats.ChainID,
		TotalExecutions: stats.TotalExecutions,
		SuccessCount:    stats.SuccessCount,
		FailureCount:    stats.FailureCount,
		AvgDuration:     stats.AvgDuration,
		LastExecution:   stats.LastExecution,
	}, nil
}

// GetNodeStats 获取节点统计
func (m *DefaultExecutionMonitor) GetNodeStats(chainID, nodeID string) (*NodeStats, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	key := fmt.Sprintf("%s:%s", chainID, nodeID)
	stats, exists := m.nodeStats[key]
	if !exists {
		return nil, fmt.Errorf("节点统计不存在: %s/%s", chainID, nodeID)
	}
	
	// 返回副本，避免并发修改
	return &NodeStats{
		ChainID:         stats.ChainID,
		NodeID:          stats.NodeID,
		NodeType:        stats.NodeType,
		ExecutionCount:  stats.ExecutionCount,
		SuccessCount:    stats.SuccessCount,
		FailureCount:    stats.FailureCount,
		AvgDuration:     stats.AvgDuration,
		LastExecution:   stats.LastExecution,
	}, nil
}

// RecordNodeExecution 记录节点执行统计
func (m *DefaultExecutionMonitor) RecordNodeExecution(chainID, nodeID, nodeType string, duration time.Duration, success bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	key := fmt.Sprintf("%s:%s", chainID, nodeID)
	nodeStats, exists := m.nodeStats[key]
	if !exists {
		nodeStats = &NodeStats{
			ChainID:  chainID,
			NodeID:   nodeID,
			NodeType: nodeType,
		}
		m.nodeStats[key] = nodeStats
	}
	
	nodeStats.ExecutionCount++
	if success {
		nodeStats.SuccessCount++
	} else {
		nodeStats.FailureCount++
	}
	
	// 更新平均执行时间
	if nodeStats.ExecutionCount == 1 {
		nodeStats.AvgDuration = duration
	} else {
		nodeStats.AvgDuration = time.Duration(
			(int64(nodeStats.AvgDuration)*int64(nodeStats.ExecutionCount-1) + int64(duration)) / int64(nodeStats.ExecutionCount),
		)
	}
	
	nodeStats.LastExecution = time.Now()
}

// GetAllChainStats 获取所有规则链统计
func (m *DefaultExecutionMonitor) GetAllChainStats() map[string]*ExecutionStats {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make(map[string]*ExecutionStats)
	for chainID, stats := range m.chainStats {
		result[chainID] = &ExecutionStats{
			ChainID:         stats.ChainID,
			TotalExecutions: stats.TotalExecutions,
			SuccessCount:    stats.SuccessCount,
			FailureCount:    stats.FailureCount,
			AvgDuration:     stats.AvgDuration,
			LastExecution:   stats.LastExecution,
		}
	}
	
	return result
}

// GetAllNodeStats 获取所有节点统计
func (m *DefaultExecutionMonitor) GetAllNodeStats() map[string]*NodeStats {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make(map[string]*NodeStats)
	for key, stats := range m.nodeStats {
		result[key] = &NodeStats{
			ChainID:         stats.ChainID,
			NodeID:          stats.NodeID,
			NodeType:        stats.NodeType,
			ExecutionCount:  stats.ExecutionCount,
			SuccessCount:    stats.SuccessCount,
			FailureCount:    stats.FailureCount,
			AvgDuration:     stats.AvgDuration,
			LastExecution:   stats.LastExecution,
		}
	}
	
	return result
}

// ClearStats 清理统计数据
func (m *DefaultExecutionMonitor) ClearStats() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.chainStats = make(map[string]*ExecutionStats)
	m.nodeStats = make(map[string]*NodeStats)
	
	m.logger.Info("清理统计数据")
}

// ClearChainStats 清理指定规则链的统计数据
func (m *DefaultExecutionMonitor) ClearChainStats(chainID string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	delete(m.chainStats, chainID)
	
	// 清理相关的节点统计
	for key := range m.nodeStats {
		if len(key) > len(chainID) && key[:len(chainID)] == chainID && key[len(chainID)] == ':' {
			delete(m.nodeStats, key)
		}
	}
	
	m.logger.Info("清理规则链统计数据", zap.String("chain_id", chainID))
}

// Stop 停止监控器
func (m *DefaultExecutionMonitor) Stop() {
	if m.cleanupTicker != nil {
		m.cleanupTicker.Stop()
	}
	
	close(m.stopChan)
	m.logger.Info("停止执行监控器")
}

// cleanupLoop 清理循环
func (m *DefaultExecutionMonitor) cleanupLoop() {
	for {
		select {
		case <-m.cleanupTicker.C:
			m.cleanupOldStats()
		case <-m.stopChan:
			return
		}
	}
}

// cleanupOldStats 清理过期统计数据
func (m *DefaultExecutionMonitor) cleanupOldStats() {
	if m.config.StatsRetention <= 0 {
		return
	}
	
	m.mu.Lock()
	defer m.mu.Unlock()
	
	cutoff := time.Now().Add(-m.config.StatsRetention)
	
	// 清理过期的规则链统计
	for chainID, stats := range m.chainStats {
		if stats.LastExecution.Before(cutoff) {
			delete(m.chainStats, chainID)
		}
	}
	
	// 清理过期的节点统计
	for key, stats := range m.nodeStats {
		if stats.LastExecution.Before(cutoff) {
			delete(m.nodeStats, key)
		}
	}
	
	m.logger.Debug("清理过期统计数据", zap.Time("cutoff", cutoff))
}

// NoOpMonitor 空操作监控器（用于禁用监控）
type NoOpMonitor struct{}

// RecordExecution 记录执行统计
func (m *NoOpMonitor) RecordExecution(ctx *ExecutionContext, result *ExecutionResult) {
	// 空操作
}

// GetExecutionStats 获取执行统计
func (m *NoOpMonitor) GetExecutionStats(chainID string) (*ExecutionStats, error) {
	return &ExecutionStats{
		ChainID: chainID,
	}, nil
}

// GetNodeStats 获取节点统计
func (m *NoOpMonitor) GetNodeStats(chainID, nodeID string) (*NodeStats, error) {
	return &NodeStats{
		ChainID:  chainID,
		NodeID:   nodeID,
		NodeType: "unknown",
	}, nil
}
