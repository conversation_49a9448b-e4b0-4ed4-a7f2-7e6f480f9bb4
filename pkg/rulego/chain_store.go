// Package rulego 规则链存储实现
package rulego

import (
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// DatabaseChainStore 数据库规则链存储
type DatabaseChainStore struct {
	db     *gorm.DB
	logger *zap.Logger
}

// RuleChainModel 规则链数据模型
type RuleChainModel struct {
	ID          string    `gorm:"primaryKey;size:255" json:"id"`
	Name        string    `gorm:"size:255;not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	Version     string    `gorm:"size:50" json:"version"`
	Definition  string    `gorm:"type:text;not null" json:"definition"` // JSON格式的规则链定义
	Status      string    `gorm:"size:50;default:draft" json:"status"`  // draft, active, inactive
	CreatedBy   string    `gorm:"size:255" json:"created_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (RuleChainModel) TableName() string {
	return "rule_chains"
}

// NewDatabaseChainStore 创建数据库规则链存储
func NewDatabaseChainStore(config DatabaseConfig, logger *zap.Logger) (*DatabaseChainStore, error) {
	var db *gorm.DB
	var err error
	
	switch config.Driver {
	case "sqlite":
		db, err = gorm.Open(sqlite.Open(config.DSN), &gorm.Config{})
	default:
		return nil, fmt.Errorf("不支持的数据库驱动: %s", config.Driver)
	}
	
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}
	
	store := &DatabaseChainStore{
		db:     db,
		logger: logger,
	}
	
	// 自动迁移表结构
	if err := store.migrate(); err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %w", err)
	}
	
	return store, nil
}

// migrate 执行数据库迁移
func (s *DatabaseChainStore) migrate() error {
	return s.db.AutoMigrate(&RuleChainModel{})
}

// GetChain 获取规则链
func (s *DatabaseChainStore) GetChain(chainID string) (*ChainDefinition, error) {
	var model RuleChainModel
	if err := s.db.Where("id = ?", chainID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("规则链不存在: %s", chainID)
		}
		return nil, fmt.Errorf("查询规则链失败: %w", err)
	}
	
	return s.modelToDefinition(&model)
}

// SaveChain 保存规则链
func (s *DatabaseChainStore) SaveChain(chain *ChainDefinition) error {
	model, err := s.definitionToModel(chain)
	if err != nil {
		return fmt.Errorf("转换规则链定义失败: %w", err)
	}
	
	// 检查是否已存在
	var existing RuleChainModel
	err = s.db.Where("id = ?", chain.ID).First(&existing).Error
	
	if err == gorm.ErrRecordNotFound {
		// 创建新记录
		model.CreatedAt = time.Now()
		model.UpdatedAt = time.Now()
		if err := s.db.Create(model).Error; err != nil {
			return fmt.Errorf("创建规则链失败: %w", err)
		}
		s.logger.Info("创建规则链", zap.String("chain_id", chain.ID))
	} else if err == nil {
		// 更新现有记录
		model.CreatedAt = existing.CreatedAt // 保持原创建时间
		model.UpdatedAt = time.Now()
		if err := s.db.Where("id = ?", chain.ID).Updates(model).Error; err != nil {
			return fmt.Errorf("更新规则链失败: %w", err)
		}
		s.logger.Info("更新规则链", zap.String("chain_id", chain.ID))
	} else {
		return fmt.Errorf("查询规则链失败: %w", err)
	}
	
	return nil
}

// DeleteChain 删除规则链
func (s *DatabaseChainStore) DeleteChain(chainID string) error {
	result := s.db.Where("id = ?", chainID).Delete(&RuleChainModel{})
	if result.Error != nil {
		return fmt.Errorf("删除规则链失败: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("规则链不存在: %s", chainID)
	}
	
	s.logger.Info("删除规则链", zap.String("chain_id", chainID))
	return nil
}

// ListChains 列出所有规则链
func (s *DatabaseChainStore) ListChains() ([]*ChainDefinition, error) {
	var models []RuleChainModel
	if err := s.db.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("查询规则链列表失败: %w", err)
	}
	
	chains := make([]*ChainDefinition, 0, len(models))
	for _, model := range models {
		chain, err := s.modelToDefinition(&model)
		if err != nil {
			s.logger.Error("转换规则链定义失败", 
				zap.String("chain_id", model.ID),
				zap.Error(err))
			continue
		}
		chains = append(chains, chain)
	}
	
	return chains, nil
}

// GetChainsByType 根据类型获取规则链
func (s *DatabaseChainStore) GetChainsByType(chainType string) ([]*ChainDefinition, error) {
	// 这里可以根据规则链定义中的类型字段进行过滤
	// 暂时返回所有规则链
	return s.ListChains()
}

// GetActiveChains 获取活跃的规则链
func (s *DatabaseChainStore) GetActiveChains() ([]*ChainDefinition, error) {
	var models []RuleChainModel
	if err := s.db.Where("status = ?", "active").Find(&models).Error; err != nil {
		return nil, fmt.Errorf("查询活跃规则链失败: %w", err)
	}
	
	chains := make([]*ChainDefinition, 0, len(models))
	for _, model := range models {
		chain, err := s.modelToDefinition(&model)
		if err != nil {
			s.logger.Error("转换规则链定义失败", 
				zap.String("chain_id", model.ID),
				zap.Error(err))
			continue
		}
		chains = append(chains, chain)
	}
	
	return chains, nil
}

// UpdateChainStatus 更新规则链状态
func (s *DatabaseChainStore) UpdateChainStatus(chainID, status string) error {
	result := s.db.Model(&RuleChainModel{}).
		Where("id = ?", chainID).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		})
	
	if result.Error != nil {
		return fmt.Errorf("更新规则链状态失败: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("规则链不存在: %s", chainID)
	}
	
	s.logger.Info("更新规则链状态", 
		zap.String("chain_id", chainID),
		zap.String("status", status))
	
	return nil
}

// modelToDefinition 将数据模型转换为规则链定义
func (s *DatabaseChainStore) modelToDefinition(model *RuleChainModel) (*ChainDefinition, error) {
	var ruleChain map[string]interface{}
	if err := json.Unmarshal([]byte(model.Definition), &ruleChain); err != nil {
		return nil, fmt.Errorf("解析规则链定义失败: %w", err)
	}
	
	return &ChainDefinition{
		ID:          model.ID,
		Name:        model.Name,
		Description: model.Description,
		Version:     model.Version,
		RuleChain:   ruleChain,
	}, nil
}

// definitionToModel 将规则链定义转换为数据模型
func (s *DatabaseChainStore) definitionToModel(chain *ChainDefinition) (*RuleChainModel, error) {
	definitionBytes, err := json.Marshal(chain.RuleChain)
	if err != nil {
		return nil, fmt.Errorf("序列化规则链定义失败: %w", err)
	}
	
	return &RuleChainModel{
		ID:          chain.ID,
		Name:        chain.Name,
		Description: chain.Description,
		Version:     chain.Version,
		Definition:  string(definitionBytes),
		Status:      "active", // 默认状态
	}, nil
}

// NoOpChainStore 空操作规则链存储（用于测试）
type NoOpChainStore struct{}

// GetChain 获取规则链
func (s *NoOpChainStore) GetChain(chainID string) (*ChainDefinition, error) {
	return nil, fmt.Errorf("规则链不存在: %s", chainID)
}

// SaveChain 保存规则链
func (s *NoOpChainStore) SaveChain(chain *ChainDefinition) error {
	return nil
}

// DeleteChain 删除规则链
func (s *NoOpChainStore) DeleteChain(chainID string) error {
	return nil
}

// ListChains 列出所有规则链
func (s *NoOpChainStore) ListChains() ([]*ChainDefinition, error) {
	return []*ChainDefinition{}, nil
}

// GetChainsByType 根据类型获取规则链
func (s *NoOpChainStore) GetChainsByType(chainType string) ([]*ChainDefinition, error) {
	return []*ChainDefinition{}, nil
}
