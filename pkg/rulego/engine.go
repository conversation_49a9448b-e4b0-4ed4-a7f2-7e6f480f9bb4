// Package rulego RuleGo 引擎封装
package rulego

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/rulego/rulego"
	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// AIOpsRuleEngine AIOps 规则引擎
type AIOpsRuleEngine struct {
	ruleEngine  *rulego.RuleEngine
	nodeManager *NodeManager
	chainStore  ChainStore
	monitor     ExecutionMonitor
	config      Config
	logger      *zap.Logger
	// 规则链缓存
	chains map[string]*ChainDefinition
	mu     sync.RWMutex
}

// NewAIOpsRuleEngine 创建 AIOps 规则引擎
func NewAIOpsRuleEngine(config Config, logger *zap.Logger) (*AIOpsRuleEngine, error) {
	// 初始化 RuleGo 引擎
	engine, err := rulego.New("aiops", nil)
	if err != nil {
		return nil, fmt.Errorf("创建 RuleGo 引擎失败: %w", err)
	}

	// 创建节点管理器
	nodeManager := NewNodeManager(logger)

	// 注册 AIOps 自定义节点
	if err := nodeManager.RegisterAIOpsNodes(engine); err != nil {
		return nil, fmt.Errorf("注册 AIOps 节点失败: %w", err)
	}

	// 创建规则链存储
	chainStore, err := NewDatabaseChainStore(config.Database, logger)
	if err != nil {
		return nil, fmt.Errorf("创建规则链存储失败: %w", err)
	}

	// 创建执行监控
	var monitor ExecutionMonitor
	if config.Monitor.Enabled {
		monitor = NewExecutionMonitor(config.Monitor, logger)
	} else {
		monitor = &NoOpMonitor{}
	}

	return &AIOpsRuleEngine{
		ruleEngine:  engine,
		nodeManager: nodeManager,
		chainStore:  chainStore,
		monitor:     monitor,
		config:      config,
		logger:      logger,
		chains:      make(map[string]*ChainDefinition),
	}, nil
}

// LoadChain 加载规则链
func (e *AIOpsRuleEngine) LoadChain(chainID string) error {
	e.logger.Info("加载规则链", zap.String("chain_id", chainID))

	// 从存储加载规则链配置
	chainDef, err := e.chainStore.GetChain(chainID)
	if err != nil {
		return fmt.Errorf("获取规则链配置失败: %w", err)
	}

	// 转换为 RuleGo 格式
	ruleChainDef, err := e.convertToRuleGoFormat(chainDef)
	if err != nil {
		return fmt.Errorf("转换规则链格式失败: %w", err)
	}

	// 加载到 RuleGo 引擎
	if err := e.ruleEngine.ReloadChild(chainID, ruleChainDef); err != nil {
		return fmt.Errorf("加载规则链到引擎失败: %w", err)
	}

	// 缓存规则链定义
	e.mu.Lock()
	e.chains[chainID] = chainDef
	e.mu.Unlock()

	e.logger.Info("规则链加载成功", zap.String("chain_id", chainID))
	return nil
}

// ExecuteChain 执行规则链
func (e *AIOpsRuleEngine) ExecuteChain(chainID string, msg *AIOpsMessage) (*ExecutionResult, error) {
	startTime := time.Now()

	// 创建执行上下文
	ctx := &ExecutionContext{
		ChainID:   chainID,
		MessageID: fmt.Sprintf("%s_%d", chainID, startTime.UnixNano()),
		StartTime: startTime,
		Metadata:  make(map[string]interface{}),
	}

	e.logger.Debug("开始执行规则链",
		zap.String("chain_id", chainID),
		zap.String("message_id", ctx.MessageID),
		zap.String("message_type", string(msg.Type)))

	// 转换消息格式
	rulegoMsg := msg.ToRuleGoMsg()

	// 执行规则链
	var execErr error
	done := make(chan struct{})

	go func() {
		defer close(done)
		execErr = e.ruleEngine.OnMsg(rulegoMsg, types.WithOnEnd(func(ctx types.RuleContext, msg types.RuleMsg, err error, relationType string) {
			if err != nil {
				e.logger.Error("规则链执行出错",
					zap.String("chain_id", chainID),
					zap.Error(err))
			}
		}))
	}()

	// 等待执行完成或超时
	select {
	case <-done:
		// 执行完成
	case <-time.After(30 * time.Second):
		execErr = fmt.Errorf("规则链执行超时")
	}

	duration := time.Since(startTime)

	// 构建执行结果
	result := &ExecutionResult{
		Success:  execErr == nil,
		Duration: duration,
		Context:  ctx,
	}

	if execErr != nil {
		result.Error = execErr
		result.Message = execErr.Error()
	} else {
		result.Message = "规则链执行成功"
	}

	// 记录执行统计
	e.monitor.RecordExecution(ctx, result)

	e.logger.Debug("规则链执行完成",
		zap.String("chain_id", chainID),
		zap.String("message_id", ctx.MessageID),
		zap.Bool("success", result.Success),
		zap.Duration("duration", duration))

	return result, execErr
}

// ExecuteChainAsync 异步执行规则链
func (e *AIOpsRuleEngine) ExecuteChainAsync(chainID string, msg *AIOpsMessage) error {
	go func() {
		if _, err := e.ExecuteChain(chainID, msg); err != nil {
			e.logger.Error("异步执行规则链失败",
				zap.String("chain_id", chainID),
				zap.Error(err))
		}
	}()
	return nil
}

// CreateChain 创建规则链
func (e *AIOpsRuleEngine) CreateChain(chainDef *ChainDefinition) error {
	e.logger.Info("创建规则链", zap.String("chain_id", chainDef.ID))

	// 验证规则链定义
	if err := e.validateChainDefinition(chainDef); err != nil {
		return fmt.Errorf("规则链定义验证失败: %w", err)
	}

	// 保存到存储
	if err := e.chainStore.SaveChain(chainDef); err != nil {
		return fmt.Errorf("保存规则链失败: %w", err)
	}

	// 加载到引擎
	if err := e.LoadChain(chainDef.ID); err != nil {
		return fmt.Errorf("加载规则链失败: %w", err)
	}

	e.logger.Info("规则链创建成功", zap.String("chain_id", chainDef.ID))
	return nil
}

// UpdateChain 更新规则链
func (e *AIOpsRuleEngine) UpdateChain(chainDef *ChainDefinition) error {
	e.logger.Info("更新规则链", zap.String("chain_id", chainDef.ID))

	// 验证规则链定义
	if err := e.validateChainDefinition(chainDef); err != nil {
		return fmt.Errorf("规则链定义验证失败: %w", err)
	}

	// 保存到存储
	if err := e.chainStore.SaveChain(chainDef); err != nil {
		return fmt.Errorf("保存规则链失败: %w", err)
	}

	// 重新加载到引擎
	if err := e.LoadChain(chainDef.ID); err != nil {
		return fmt.Errorf("重新加载规则链失败: %w", err)
	}

	e.logger.Info("规则链更新成功", zap.String("chain_id", chainDef.ID))
	return nil
}

// DeleteChain 删除规则链
func (e *AIOpsRuleEngine) DeleteChain(chainID string) error {
	e.logger.Info("删除规则链", zap.String("chain_id", chainID))

	// 从引擎中移除
	e.ruleEngine.Stop()

	// 从存储中删除
	if err := e.chainStore.DeleteChain(chainID); err != nil {
		return fmt.Errorf("删除规则链失败: %w", err)
	}

	// 从缓存中移除
	e.mu.Lock()
	delete(e.chains, chainID)
	e.mu.Unlock()

	e.logger.Info("规则链删除成功", zap.String("chain_id", chainID))
	return nil
}

// GetChain 获取规则链
func (e *AIOpsRuleEngine) GetChain(chainID string) (*ChainDefinition, error) {
	e.mu.RLock()
	chain, exists := e.chains[chainID]
	e.mu.RUnlock()

	if exists {
		return chain, nil
	}

	// 从存储加载
	return e.chainStore.GetChain(chainID)
}

// ListChains 列出所有规则链
func (e *AIOpsRuleEngine) ListChains() ([]*ChainDefinition, error) {
	return e.chainStore.ListChains()
}

// GetExecutionStats 获取执行统计
func (e *AIOpsRuleEngine) GetExecutionStats(chainID string) (*ExecutionStats, error) {
	return e.monitor.GetExecutionStats(chainID)
}

// Stop 停止引擎
func (e *AIOpsRuleEngine) Stop() {
	e.logger.Info("停止 AIOps 规则引擎")
	e.ruleEngine.Stop()
}

// convertToRuleGoFormat 转换为 RuleGo 格式
func (e *AIOpsRuleEngine) convertToRuleGoFormat(chainDef *ChainDefinition) (string, error) {
	// 这里需要将我们的规则链定义转换为 RuleGo 的 JSON 格式
	// 暂时直接返回 RuleChain 字段的 JSON
	ruleChainBytes, err := jsonMarshal(chainDef.RuleChain)
	if err != nil {
		return "", err
	}
	return string(ruleChainBytes), nil
}

// validateChainDefinition 验证规则链定义
func (e *AIOpsRuleEngine) validateChainDefinition(chainDef *ChainDefinition) error {
	if chainDef.ID == "" {
		return fmt.Errorf("规则链 ID 不能为空")
	}
	if chainDef.Name == "" {
		return fmt.Errorf("规则链名称不能为空")
	}
	if chainDef.RuleChain == nil {
		return fmt.Errorf("规则链定义不能为空")
	}
	return nil
}

// jsonMarshal JSON 序列化辅助函数
func jsonMarshal(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}
