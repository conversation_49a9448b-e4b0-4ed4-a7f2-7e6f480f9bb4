// Package rulego 节点管理器
package rulego

import (
	"fmt"

	"github.com/rulego/rulego"
	"github.com/rulego/rulego/api/types"
	"go.uber.org/zap"
)

// NodeManager 节点管理器
type NodeManager struct {
	logger *zap.Logger
	nodes  map[string]types.Node
}

// NewNodeManager 创建节点管理器
func NewNodeManager(logger *zap.Logger) *NodeManager {
	return &NodeManager{
		logger: logger,
		nodes:  make(map[string]types.Node),
	}
}

// RegisterAIOpsNodes 注册 AIOps 自定义节点
func (nm *NodeManager) RegisterAIOpsNodes(engine *rulego.RuleEngine) error {
	nm.logger.Info("注册 AIOps 自定义节点")

	// 注册数据分析节点
	if err := nm.registerNode(engine, "aiops/analysis", NewAnalysisNode()); err != nil {
		return fmt.Errorf("注册分析节点失败: %w", err)
	}

	// 注册告警节点
	if err := nm.registerNode(engine, "aiops/alert", NewAlertNode()); err != nil {
		return fmt.Errorf("注册告警节点失败: %w", err)
	}

	// 注册扩缩容节点
	if err := nm.registerNode(engine, "aiops/scale", NewAutoScaleNode()); err != nil {
		return fmt.Errorf("注册扩缩容节点失败: %w", err)
	}

	// 注册指标过滤节点
	if err := nm.registerNode(engine, "aiops/metricFilter", NewMetricFilterNode()); err != nil {
		return fmt.Errorf("注册指标过滤节点失败: %w", err)
	}

	// 注册阈值检查节点
	if err := nm.registerNode(engine, "aiops/threshold", NewThresholdNode()); err != nil {
		return fmt.Errorf("注册阈值检查节点失败: %w", err)
	}

	// 注册异常检测节点
	if err := nm.registerNode(engine, "aiops/anomaly", NewAnomalyDetectionNode()); err != nil {
		return fmt.Errorf("注册异常检测节点失败: %w", err)
	}

	// 注册数据转换节点
	if err := nm.registerNode(engine, "aiops/transform", NewDataTransformNode()); err != nil {
		return fmt.Errorf("注册数据转换节点失败: %w", err)
	}

	// 注册日志节点
	if err := nm.registerNode(engine, "aiops/log", NewLogNode()); err != nil {
		return fmt.Errorf("注册日志节点失败: %w", err)
	}

	nm.logger.Info("AIOps 自定义节点注册完成")
	return nil
}

// registerNode 注册单个节点
func (nm *NodeManager) registerNode(engine *rulego.RuleEngine, nodeType string, node types.Node) error {
	nm.logger.Debug("注册节点", zap.String("type", nodeType))

	// 注册到 RuleGo 引擎
	engine.Registry().Register(nodeType, node)

	// 缓存节点实例
	nm.nodes[nodeType] = node

	return nil
}

// GetNode 获取节点实例
func (nm *NodeManager) GetNode(nodeType string) (types.Node, bool) {
	node, exists := nm.nodes[nodeType]
	return node, exists
}

// ListNodes 列出所有注册的节点类型
func (nm *NodeManager) ListNodes() []string {
	var nodeTypes []string
	for nodeType := range nm.nodes {
		nodeTypes = append(nodeTypes, nodeType)
	}
	return nodeTypes
}

// UnregisterNode 注销节点
func (nm *NodeManager) UnregisterNode(nodeType string) {
	delete(nm.nodes, nodeType)
	nm.logger.Debug("注销节点", zap.String("type", nodeType))
}

// RegisterCustomNode 注册自定义节点
func (nm *NodeManager) RegisterCustomNode(engine rulego.RuleEngine, nodeType string, node types.Node) error {
	return nm.registerNode(engine, nodeType, node)
}
