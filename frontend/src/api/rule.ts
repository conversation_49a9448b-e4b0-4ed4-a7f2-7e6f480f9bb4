import { request } from '@/utils/request'
import type { Rule, CreateRuleRequest, UpdateRuleRequest } from '@/types/rule'

export interface RulesResponse {
  rules: Rule[]
  total: number
}

export interface RuleResponse {
  rule: Rule
}

export interface CategoriesResponse {
  categories: string[]
}

export interface ActionTypesResponse {
  action_types: string[]
}

export const ruleApi = {
  // 获取规则列表
  getRules: (params?: {
    category?: string
    task_type?: string
    enabled?: boolean
  }) => {
    return request.get<RulesResponse>('/api/v1/rules', { params })
  },

  // 获取单个规则
  getRule: (id: string) => {
    return request.get<RuleResponse>(`/api/v1/rules/${id}`)
  },

  // 创建规则
  createRule: (data: CreateRuleRequest) => {
    return request.post<RuleResponse>('/api/v1/rules', data)
  },

  // 更新规则
  updateRule: (id: string, data: UpdateRuleRequest) => {
    return request.put<RuleResponse>(`/api/v1/rules/${id}`, data)
  },

  // 删除规则
  deleteRule: (id: string) => {
    return request.delete(`/api/v1/rules/${id}`)
  },

  // 启用规则
  enableRule: (id: string) => {
    return request.post(`/api/v1/rules/${id}/enable`)
  },

  // 禁用规则
  disableRule: (id: string) => {
    return request.post(`/api/v1/rules/${id}/disable`)
  },

  // 获取规则分类列表
  getCategories: () => {
    return request.get<CategoriesResponse>('/api/v1/rules/categories')
  },

  // 获取动作类型列表
  getActionTypes: () => {
    return request.get<ActionTypesResponse>('/api/v1/rules/action-types')
  }
}
