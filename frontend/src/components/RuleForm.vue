<template>
  <a-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    layout="vertical"
    class="space-y-4"
  >
    <div class="grid grid-cols-2 gap-4">
      <a-form-item label="规则ID" field="id" :disabled="isEditing">
        <a-input 
          v-model="formData.id" 
          placeholder="输入规则ID（唯一标识）"
          :disabled="isEditing"
        />
      </a-form-item>
      
      <a-form-item label="规则名称" field="name">
        <a-input v-model="formData.name" placeholder="输入规则名称" />
      </a-form-item>
    </div>

    <a-form-item label="规则描述" field="description">
      <a-textarea 
        v-model="formData.description" 
        placeholder="描述规则的功能和用途"
        :rows="3"
      />
    </a-form-item>

    <div class="grid grid-cols-3 gap-4">
      <a-form-item label="规则分类" field="category">
        <a-select v-model="formData.category" placeholder="选择分类">
          <a-option value="threshold">阈值监控</a-option>
          <a-option value="anomaly">异常检测</a-option>
          <a-option value="performance">性能监控</a-option>
          <a-option value="system">系统监控</a-option>
          <a-option value="custom">自定义</a-option>
        </a-select>
      </a-form-item>

      <a-form-item label="任务类型" field="taskType">
        <a-select v-model="formData.taskType" placeholder="选择任务类型">
          <a-option value="system_monitoring">系统监控</a-option>
          <a-option value="mysql_monitoring">MySQL监控</a-option>
          <a-option value="log_analysis">日志分析</a-option>
          <a-option value="custom_metrics">自定义指标</a-option>
        </a-select>
      </a-form-item>

      <a-form-item label="优先级" field="priority">
        <a-input-number 
          v-model="formData.priority" 
          :min="1" 
          :max="10"
          placeholder="1-10"
        />
      </a-form-item>
    </div>

    <a-form-item label="触发条件" field="condition">
      <div class="space-y-3">
        <a-textarea 
          v-model="formData.condition" 
          placeholder="输入条件表达式，例如：cpu_usage > 80"
          :rows="4"
        />
        <div class="text-sm text-gray-500">
          <p>支持的表达式示例：</p>
          <ul class="list-disc list-inside space-y-1 mt-1">
            <li>cpu_usage > 80 - CPU使用率超过80%</li>
            <li>memory_usage > 90 && cpu_usage > 70 - 内存和CPU同时高负载</li>
            <li>disk_space < 10 - 磁盘空间小于10%</li>
            <li>response_time > 1000 - 响应时间超过1秒</li>
          </ul>
        </div>
      </div>
    </a-form-item>

    <div class="grid grid-cols-2 gap-4">
      <a-form-item label="动作类型" field="actionType">
        <a-select v-model="formData.actionType" placeholder="选择动作类型" @change="onActionTypeChange">
          <a-option value="alert">生成告警</a-option>
          <a-option value="email">发送邮件</a-option>
          <a-option value="webhook">调用Webhook</a-option>
          <a-option value="log">记录日志</a-option>
          <a-option value="script">执行脚本</a-option>
        </a-select>
      </a-form-item>

      <a-form-item label="启用状态" field="isEnabled">
        <a-switch v-model="formData.isEnabled" />
        <span class="ml-2 text-sm text-gray-600">
          {{ formData.isEnabled ? '已启用' : '已禁用' }}
        </span>
      </a-form-item>
    </div>

    <!-- 动作配置 -->
    <a-form-item label="动作配置" field="actionConfig">
      <div class="border rounded-lg p-4 bg-gray-50">
        <!-- 告警配置 -->
        <div v-if="formData.actionType === 'alert'" class="space-y-3">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">告警级别</label>
              <a-select v-model="formData.actionConfig.severity" placeholder="选择告警级别">
                <a-option value="low">低</a-option>
                <a-option value="medium">中</a-option>
                <a-option value="high">高</a-option>
                <a-option value="critical">严重</a-option>
              </a-select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">通知渠道</label>
              <a-select 
                v-model="formData.actionConfig.channels" 
                placeholder="选择通知渠道"
                multiple
              >
                <a-option value="email">邮件</a-option>
                <a-option value="sms">短信</a-option>
                <a-option value="webhook">Webhook</a-option>
              </a-select>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">告警消息</label>
            <a-textarea 
              v-model="formData.actionConfig.message" 
              placeholder="告警消息模板"
              :rows="3"
            />
          </div>
        </div>

        <!-- 邮件配置 -->
        <div v-else-if="formData.actionType === 'email'" class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">收件人</label>
            <a-select 
              v-model="formData.actionConfig.recipients" 
              placeholder="输入邮箱地址"
              multiple
              allow-create
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">邮件主题</label>
            <a-input v-model="formData.actionConfig.subject" placeholder="邮件主题模板" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">邮件模板</label>
            <a-textarea 
              v-model="formData.actionConfig.template" 
              placeholder="邮件内容模板"
              :rows="4"
            />
          </div>
          <div>
            <a-checkbox v-model="formData.actionConfig.attachLogs">
              附加相关日志
            </a-checkbox>
          </div>
        </div>

        <!-- Webhook配置 -->
        <div v-else-if="formData.actionType === 'webhook'" class="space-y-3">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">URL</label>
              <a-input v-model="formData.actionConfig.url" placeholder="Webhook URL" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">请求方法</label>
              <a-select v-model="formData.actionConfig.method">
                <a-option value="POST">POST</a-option>
                <a-option value="PUT">PUT</a-option>
              </a-select>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">请求头</label>
            <a-textarea 
              v-model="headersText" 
              placeholder='{"Content-Type": "application/json"}'
              :rows="3"
              @blur="updateHeaders"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">请求体</label>
            <a-textarea 
              v-model="payloadText" 
              placeholder='{"message": "{{message}}", "level": "{{level}}"}'
              :rows="4"
              @blur="updatePayload"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">超时时间（秒）</label>
            <a-input-number v-model="formData.actionConfig.timeout" :min="1" :max="300" />
          </div>
        </div>

        <!-- 脚本配置 -->
        <div v-else-if="formData.actionType === 'script'" class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">脚本路径</label>
            <a-input v-model="formData.actionConfig.scriptPath" placeholder="/path/to/script.sh" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">参数</label>
            <a-select 
              v-model="formData.actionConfig.arguments" 
              placeholder="脚本参数"
              multiple
              allow-create
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">超时时间（秒）</label>
            <a-input-number v-model="formData.actionConfig.timeout" :min="1" :max="300" />
          </div>
        </div>

        <!-- 日志配置 -->
        <div v-else-if="formData.actionType === 'log'" class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">日志级别</label>
            <a-select v-model="formData.actionConfig.level">
              <a-option value="debug">DEBUG</a-option>
              <a-option value="info">INFO</a-option>
              <a-option value="warn">WARN</a-option>
              <a-option value="error">ERROR</a-option>
            </a-select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">日志消息</label>
            <a-textarea 
              v-model="formData.actionConfig.message" 
              placeholder="日志消息模板"
              :rows="3"
            />
          </div>
        </div>
      </div>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { Rule, CreateRuleRequest, UpdateRuleRequest } from '@/types/rule'

interface Props {
  rule?: Rule | null
  isEditing: boolean
}

const props = defineProps<Props>()

const formRef = ref()
const formData = reactive<CreateRuleRequest>({
  id: '',
  name: '',
  description: '',
  category: 'threshold',
  taskType: '',
  condition: '',
  actionType: 'alert',
  actionConfig: {},
  priority: 5,
  isEnabled: true
})

// 表单验证规则
const rules = {
  id: [
    { required: true, message: '请输入规则ID' },
    { 
      pattern: /^[a-zA-Z0-9_-]+$/, 
      message: 'ID只能包含字母、数字、下划线和短横线' 
    }
  ],
  name: [
    { required: true, message: '请输入规则名称' },
    { minLength: 2, message: '规则名称至少2个字符' }
  ],
  category: [
    { required: true, message: '请选择规则分类' }
  ],
  taskType: [
    { required: true, message: '请选择任务类型' }
  ],
  condition: [
    { required: true, message: '请输入触发条件' }
  ],
  actionType: [
    { required: true, message: '请选择动作类型' }
  ],
  priority: [
    { required: true, message: '请设置优先级' },
    { type: 'number', min: 1, max: 10, message: '优先级必须在1-10之间' }
  ]
}

// 用于处理复杂对象的文本表示
const headersText = ref('')
const payloadText = ref('')

// 监听props变化，初始化表单数据
watch(
  () => props.rule,
  (newRule) => {
    if (newRule) {
      Object.assign(formData, {
        id: newRule.id,
        name: newRule.name,
        description: newRule.description,
        category: newRule.category,
        taskType: newRule.taskType,
        condition: newRule.condition,
        actionType: newRule.actionType,
        actionConfig: { ...newRule.actionConfig },
        priority: newRule.priority,
        isEnabled: newRule.isEnabled
      })
      
      // 初始化复杂对象的文本表示
      if (newRule.actionConfig.headers) {
        headersText.value = JSON.stringify(newRule.actionConfig.headers, null, 2)
      }
      if (newRule.actionConfig.payload) {
        payloadText.value = JSON.stringify(newRule.actionConfig.payload, null, 2)
      }
    } else {
      // 重置表单
      Object.assign(formData, {
        id: '',
        name: '',
        description: '',
        category: 'threshold',
        taskType: '',
        condition: '',
        actionType: 'alert',
        actionConfig: {},
        priority: 5,
        isEnabled: true
      })
      headersText.value = ''
      payloadText.value = ''
    }
  },
  { immediate: true }
)

// 动作类型改变时重置配置
const onActionTypeChange = () => {
  formData.actionConfig = {}
  headersText.value = ''
  payloadText.value = ''
  
  // 设置默认值
  switch (formData.actionType) {
    case 'alert':
      formData.actionConfig = {
        severity: 'medium',
        message: '',
        channels: []
      }
      break
    case 'email':
      formData.actionConfig = {
        recipients: [],
        subject: '',
        template: '',
        attachLogs: false
      }
      break
    case 'webhook':
      formData.actionConfig = {
        url: '',
        method: 'POST',
        headers: {},
        payload: {},
        timeout: 30
      }
      break
    case 'script':
      formData.actionConfig = {
        scriptPath: '',
        arguments: [],
        timeout: 60,
        environment: {}
      }
      break
    case 'log':
      formData.actionConfig = {
        level: 'info',
        message: ''
      }
      break
  }
}

// 更新headers对象
const updateHeaders = () => {
  try {
    if (headersText.value.trim()) {
      formData.actionConfig.headers = JSON.parse(headersText.value)
    }
  } catch (error) {
    console.error('Invalid headers JSON:', error)
  }
}

// 更新payload对象
const updatePayload = () => {
  try {
    if (payloadText.value.trim()) {
      formData.actionConfig.payload = JSON.parse(payloadText.value)
    }
  } catch (error) {
    console.error('Invalid payload JSON:', error)
  }
}

// 表单验证方法
const validate = async () => {
  await formRef.value.validate()
  return formData
}

// 暴露方法给父组件
defineExpose({
  validate
})
</script>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}
</style>
