import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: 'AIOps 监控系统'
    }
  },
  {
    path: '/tasks',
    name: 'tasks',
    component: () => import('@/views/Tasks.vue'),
    meta: {
      title: '任务管理'
    }
  },
  {
    path: '/alerts',
    name: 'alerts',
    component: () => import('@/views/Alerts.vue'),
    meta: {
      title: '告警管理'
    }
  },
  {
    path: '/metrics',
    name: 'metrics',
    component: () => import('@/views/Metrics.vue'),
    meta: {
      title: '指标监控'
    }
  },
  {
    path: '/rules',
    name: 'rules',
    component: () => import('@/views/Rules.vue'),
    meta: {
      title: '规则管理'
    }
  },
  {
    path: '/settings',
    name: 'settings',
    component: () => import('@/views/Settings.vue'),
    meta: {
      title: '系统设置'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
