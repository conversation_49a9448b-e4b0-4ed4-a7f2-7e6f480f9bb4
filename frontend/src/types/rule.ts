export type RuleCategory = 'threshold' | 'anomaly' | 'performance' | 'system' | 'custom'
export type ActionType = 'alert' | 'email' | 'webhook' | 'log' | 'script'

export interface Rule {
  id: string
  name: string
  description: string
  category: RuleCategory
  taskType: string
  condition: string
  actionType: ActionType
  actionConfig: Record<string, any>
  priority: number
  isEnabled: boolean
  matchCount: number
  lastMatchTime?: string
  createdAt: string
  updatedAt: string
  // 前端临时状态
  _switching?: boolean
}

export interface CreateRuleRequest {
  id: string
  name: string
  description: string
  category: RuleCategory
  taskType: string
  condition: string
  actionType: ActionType
  actionConfig: Record<string, any>
  priority: number
  isEnabled: boolean
}

export interface UpdateRuleRequest {
  name: string
  description: string
  category: RuleCategory
  taskType: string
  condition: string
  actionType: ActionType
  actionConfig: Record<string, any>
  priority: number
  isEnabled: boolean
}

export interface RuleCondition {
  field: string
  operator: string
  value: any
  logic?: 'AND' | 'OR'
}

export interface AlertActionConfig {
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  channels: string[]
}

export interface EmailActionConfig {
  recipients: string[]
  subject: string
  template: string
  attachLogs?: boolean
}

export interface WebhookActionConfig {
  url: string
  method: 'POST' | 'PUT'
  headers: Record<string, string>
  payload: Record<string, any>
  timeout: number
}

export interface ScriptActionConfig {
  scriptPath: string
  arguments: string[]
  timeout: number
  environment: Record<string, string>
}
