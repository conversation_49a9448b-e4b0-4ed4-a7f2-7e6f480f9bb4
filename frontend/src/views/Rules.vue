<template>
  <Layout>
    <div class="space-y-6 fade-in">
      <!-- 页面标题和操作 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">规则管理</h1>
          <p class="text-gray-600 mt-1">管理系统监控和告警规则</p>
        </div>
        <div class="flex space-x-3">
          <a-button type="primary" @click="handleCreateRule">
            <template #icon>
              <icon-plus />
            </template>
            新建规则
          </a-button>
          <a-button @click="refreshRules" :loading="loading">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </div>
      </div>

      <!-- 规则统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="总规则数"
          :value="rules.length"
          icon="📏"
          color="blue"
        />
        <StatCard
          title="已启用"
          :value="enabledRules.length"
          icon="✅"
          color="green"
        />
        <StatCard
          title="已禁用"
          :value="disabledRules.length"
          icon="⛔"
          color="gray"
        />
        <StatCard
          title="阈值规则"
          :value="thresholdRules.length"
          icon="📊"
          color="orange"
        />
      </div>

      <!-- 过滤器 -->
      <div class="bg-white p-4 rounded-lg shadow-sm border">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">分类</label>
            <a-select 
              v-model="filters.category" 
              placeholder="选择分类"
              allow-clear
              @change="applyFilters"
            >
              <a-option value="">全部分类</a-option>
              <a-option value="threshold">阈值监控</a-option>
              <a-option value="anomaly">异常检测</a-option>
              <a-option value="performance">性能监控</a-option>
              <a-option value="system">系统监控</a-option>
              <a-option value="custom">自定义</a-option>
            </a-select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">任务类型</label>
            <a-select 
              v-model="filters.taskType" 
              placeholder="选择任务类型"
              allow-clear
              @change="applyFilters"
            >
              <a-option value="">全部类型</a-option>
              <a-option value="system_monitoring">系统监控</a-option>
              <a-option value="mysql_monitoring">MySQL监控</a-option>
              <a-option value="log_analysis">日志分析</a-option>
              <a-option value="custom_metrics">自定义指标</a-option>
            </a-select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
            <a-select 
              v-model="filters.enabled" 
              placeholder="选择状态"
              allow-clear
              @change="applyFilters"
            >
              <a-option value="">全部状态</a-option>
              <a-option :value="true">已启用</a-option>
              <a-option :value="false">已禁用</a-option>
            </a-select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">搜索</label>
            <a-input 
              v-model="filters.search" 
              placeholder="搜索规则名称..."
              @input="applyFilters"
            />
          </div>
        </div>
      </div>

      <!-- 规则列表 -->
      <div class="bg-white rounded-lg shadow-sm border">
        <a-table 
          :columns="columns" 
          :data="filteredRules"
          :loading="loading"
          :pagination="pagination"
          @pageChange="onPageChange"
          @pageSizeChange="onPageSizeChange"
        >
          <template #category="{ record }">
            <a-tag :color="getCategoryColor(record.category)">
              {{ getCategoryLabel(record.category) }}
            </a-tag>
          </template>
          
          <template #enabled="{ record }">
            <a-switch 
              :model-value="record.isEnabled"
              @change="handleToggleRule(record)"
              :loading="record._switching"
            />
          </template>
          
          <template #priority="{ record }">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ record.priority }}
            </a-tag>
          </template>
          
          <template #lastMatch="{ record }">
            <span v-if="record.lastMatchTime" class="text-sm text-gray-600">
              {{ formatTime(record.lastMatchTime) }}
            </span>
            <span v-else class="text-sm text-gray-400">从未匹配</span>
          </template>
          
          <template #actions="{ record }">
            <div class="flex space-x-2">
              <a-button size="small" @click="handleEditRule(record)">
                编辑
              </a-button>
              <a-button size="small" @click="handleViewRule(record)">
                查看
              </a-button>
              <a-popconfirm
                content="确定要删除这个规则吗？"
                @ok="handleDeleteRule(record)"
              >
                <a-button size="small" status="danger">
                  删除
                </a-button>
              </a-popconfirm>
            </div>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 创建/编辑规则模态框 -->
    <a-modal
      v-model:visible="ruleModalVisible"
      :title="isEditing ? '编辑规则' : '新建规则'"
      :width="800"
      @ok="handleSaveRule"
      @cancel="handleCancelRule"
    >
      <RuleForm
        ref="ruleFormRef"
        :rule="currentRule"
        :is-editing="isEditing"
      />
    </a-modal>

    <!-- 查看规则详情模态框 -->
    <a-modal
      v-model:visible="ruleDetailVisible"
      title="规则详情"
      :width="600"
      :footer="false"
    >
      <RuleDetail :rule="currentRule" />
    </a-modal>
  </Layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import Layout from '@/components/Layout.vue'
import StatCard from '@/components/StatCard.vue'
import RuleForm from '@/components/RuleForm.vue'
import RuleDetail from '@/components/RuleDetail.vue'
import { ruleApi } from '@/api/rule'
import type { Rule, RuleCategory } from '@/types/rule'

// 响应式数据
const loading = ref(false)
const rules = ref<Rule[]>([])
const ruleModalVisible = ref(false)
const ruleDetailVisible = ref(false)
const isEditing = ref(false)
const currentRule = ref<Rule | null>(null)
const ruleFormRef = ref()

// 过滤器
const filters = ref({
  category: '',
  taskType: '',
  enabled: '' as boolean | string,
  search: ''
})

// 分页
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表格列定义
const columns = [
  {
    title: '规则名称',
    dataIndex: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '分类',
    dataIndex: 'category',
    slotName: 'category',
    width: 120
  },
  {
    title: '任务类型',
    dataIndex: 'taskType',
    width: 150
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    slotName: 'priority',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'isEnabled',
    slotName: 'enabled',
    width: 80
  },
  {
    title: '匹配次数',
    dataIndex: 'matchCount',
    width: 100
  },
  {
    title: '最后匹配',
    dataIndex: 'lastMatchTime',
    slotName: 'lastMatch',
    width: 150
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 180,
    fixed: 'right'
  }
]

// 计算属性
const enabledRules = computed(() => rules.value.filter(rule => rule.isEnabled))
const disabledRules = computed(() => rules.value.filter(rule => !rule.isEnabled))
const thresholdRules = computed(() => rules.value.filter(rule => rule.category === 'threshold'))

const filteredRules = computed(() => {
  let result = rules.value

  if (filters.value.category) {
    result = result.filter(rule => rule.category === filters.value.category)
  }

  if (filters.value.taskType) {
    result = result.filter(rule => rule.taskType === filters.value.taskType)
  }

  if (filters.value.enabled !== '') {
    result = result.filter(rule => rule.isEnabled === filters.value.enabled)
  }

  if (filters.value.search) {
    const searchTerm = filters.value.search.toLowerCase()
    result = result.filter(rule => 
      rule.name.toLowerCase().includes(searchTerm) ||
      rule.description.toLowerCase().includes(searchTerm)
    )
  }

  pagination.value.total = result.length
  const start = (pagination.value.current - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return result.slice(start, end)
})

// 方法
const refreshRules = async () => {
  loading.value = true
  try {
    const response = await ruleApi.getRules()
    rules.value = response.data.rules || []
  } catch (error) {
    Message.error('获取规则列表失败')
    console.error('获取规则失败:', error)
  } finally {
    loading.value = false
  }
}

const applyFilters = () => {
  pagination.value.current = 1
}

const onPageChange = (page: number) => {
  pagination.value.current = page
}

const onPageSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.current = 1
}

const handleCreateRule = () => {
  currentRule.value = null
  isEditing.value = false
  ruleModalVisible.value = true
}

const handleEditRule = (rule: Rule) => {
  currentRule.value = { ...rule }
  isEditing.value = true
  ruleModalVisible.value = true
}

const handleViewRule = (rule: Rule) => {
  currentRule.value = rule
  ruleDetailVisible.value = true
}

const handleToggleRule = async (rule: Rule) => {
  rule._switching = true
  try {
    if (rule.isEnabled) {
      await ruleApi.disableRule(rule.id)
      rule.isEnabled = false
      Message.success('规则已禁用')
    } else {
      await ruleApi.enableRule(rule.id)
      rule.isEnabled = true
      Message.success('规则已启用')
    }
  } catch (error) {
    Message.error('操作失败')
    console.error('切换规则状态失败:', error)
  } finally {
    rule._switching = false
  }
}

const handleDeleteRule = async (rule: Rule) => {
  try {
    await ruleApi.deleteRule(rule.id)
    Message.success('规则删除成功')
    refreshRules()
  } catch (error) {
    Message.error('删除规则失败')
    console.error('删除规则失败:', error)
  }
}

const handleSaveRule = async () => {
  try {
    const formData = await ruleFormRef.value.validate()
    if (isEditing.value && currentRule.value) {
      await ruleApi.updateRule(currentRule.value.id, formData)
      Message.success('规则更新成功')
    } else {
      await ruleApi.createRule(formData)
      Message.success('规则创建成功')
    }
    ruleModalVisible.value = false
    refreshRules()
  } catch (error) {
    Message.error(isEditing.value ? '更新规则失败' : '创建规则失败')
    console.error('保存规则失败:', error)
  }
}

const handleCancelRule = () => {
  ruleModalVisible.value = false
  currentRule.value = null
}

// 辅助函数
const getCategoryColor = (category: RuleCategory): string => {
  const colors = {
    threshold: 'blue',
    anomaly: 'red',
    performance: 'orange',
    system: 'green',
    custom: 'purple'
  }
  return colors[category] || 'gray'
}

const getCategoryLabel = (category: RuleCategory): string => {
  const labels = {
    threshold: '阈值监控',
    anomaly: '异常检测',
    performance: '性能监控',
    system: '系统监控',
    custom: '自定义'
  }
  return labels[category] || category
}

const getPriorityColor = (priority: number): string => {
  if (priority >= 8) return 'red'
  if (priority >= 5) return 'orange'
  if (priority >= 3) return 'blue'
  return 'gray'
}

const formatTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  refreshRules()
})
</script>

<style scoped>
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
