package service

import (
	"time"

	"aiops/internal/model"
	"aiops/internal/repository"

	"go.uber.org/zap"
)

// TaskService 任务服务接口
type TaskService interface {
	CreateTask(task *model.Task) error
	GetTask(id string) (*model.Task, error)
	GetTasks(taskType string) ([]*model.Task, error)
	UpdateTask(id string, task *model.Task) error
	DeleteTask(id string) error
	GetTaskExecutions(taskID string, limit int) ([]*model.TaskExecution, error)
}

// MetricService 指标服务接口
type MetricService interface {
	GetMetrics(taskID, metricName string, start, end time.Time) ([]*model.MetricData, error)
	GetLatestMetric(taskID, metricName string) (*model.MetricData, error)
}

// AlertService 告警服务接口
type AlertService interface {
	GetAlerts(taskID string, limit int) ([]*model.AlertEvent, error)
	GetUnresolvedAlerts() ([]*model.AlertEvent, error)
	ResolveAlert(id uint) error
}

// LogService 日志服务接口
type LogService interface {
	GetLogs(taskID, level string, limit int) ([]*model.LogEntry, error)
}

// RuleService 规则服务接口
type RuleService interface {
	CreateRule(rule *model.Rule) error
	GetRule(id string) (*model.Rule, error)
	GetRules(category model.RuleCategory, taskType string) ([]*model.Rule, error)
	GetAllRules() ([]*model.Rule, error)
	GetEnabledRules() ([]*model.Rule, error)
	UpdateRule(id string, rule *model.Rule) error
	DeleteRule(id string) error
	EnableRule(id string) error
	DisableRule(id string) error
}

// Services 服务集合
type Services struct {
	Task   TaskService
	Metric MetricService
	Alert  AlertService
	Log    LogService
	Rule   RuleService
}

// NewServices 创建服务实例
func NewServices(repos *repository.Repositories, logger *zap.Logger) *Services {
	return &Services{
		Task:   newTaskService(repos, logger),
		Metric: newMetricService(repos, logger),
		Alert:  newAlertService(repos, logger),
		Log:    newLogService(repos, logger),
		Rule:   newRuleService(repos, logger),
	}
}
