package service

import (
	"aiops/internal/model"
	"aiops/internal/repository"

	"go.uber.org/zap"
)

// ruleService 规则服务实现
type ruleService struct {
	ruleRepo repository.RuleRepository
	logger   *zap.Logger
}

// newRuleService 创建规则服务实例
func newRuleService(repos *repository.Repositories, logger *zap.Logger) RuleService {
	return &ruleService{
		ruleRepo: repos.Rule,
		logger:   logger,
	}
}

// CreateRule 创建规则
func (s *ruleService) CreateRule(rule *model.Rule) error {
	if err := s.validateRule(rule); err != nil {
		s.logger.Error("规则验证失败", zap.Error(err), zap.String("rule_id", rule.ID))
		return err
	}

	if err := s.ruleRepo.Create(rule); err != nil {
		s.logger.Error("创建规则失败", zap.Error(err), zap.String("rule_id", rule.ID))
		return err
	}

	s.logger.Info("成功创建规则", zap.String("rule_id", rule.ID), zap.String("rule_name", rule.Name))
	return nil
}

// GetRule 获取单个规则
func (s *ruleService) GetRule(id string) (*model.Rule, error) {
	rule, err := s.ruleRepo.GetByID(id)
	if err != nil {
		s.logger.Error("获取规则失败", zap.Error(err), zap.String("rule_id", id))
		return nil, err
	}
	return rule, nil
}

// GetRules 获取规则列表
func (s *ruleService) GetRules(category model.RuleCategory, taskType string) ([]*model.Rule, error) {
	var rules []*model.Rule
	var err error

	if category != "" && taskType != "" {
		// 如果同时指定分类和任务类型，需要组合查询
		// 这里简化处理，先按分类查询，然后过滤任务类型
		rules, err = s.ruleRepo.GetByCategory(category)
		if err != nil {
			s.logger.Error("按分类获取规则失败", zap.Error(err), zap.String("category", string(category)))
			return nil, err
		}

		// 过滤任务类型
		var filteredRules []*model.Rule
		for _, rule := range rules {
			if rule.TaskType == taskType {
				filteredRules = append(filteredRules, rule)
			}
		}
		rules = filteredRules
	} else if category != "" {
		rules, err = s.ruleRepo.GetByCategory(category)
		if err != nil {
			s.logger.Error("按分类获取规则失败", zap.Error(err), zap.String("category", string(category)))
			return nil, err
		}
	} else if taskType != "" {
		rules, err = s.ruleRepo.GetByTaskType(taskType)
		if err != nil {
			s.logger.Error("按任务类型获取规则失败", zap.Error(err), zap.String("task_type", taskType))
			return nil, err
		}
	} else {
		rules, err = s.ruleRepo.GetAll()
		if err != nil {
			s.logger.Error("获取所有规则失败", zap.Error(err))
			return nil, err
		}
	}

	return rules, nil
}

// GetAllRules 获取所有规则
func (s *ruleService) GetAllRules() ([]*model.Rule, error) {
	rules, err := s.ruleRepo.GetAll()
	if err != nil {
		s.logger.Error("获取所有规则失败", zap.Error(err))
		return nil, err
	}
	return rules, nil
}

// GetEnabledRules 获取启用的规则
func (s *ruleService) GetEnabledRules() ([]*model.Rule, error) {
	rules, err := s.ruleRepo.GetEnabled()
	if err != nil {
		s.logger.Error("获取启用的规则失败", zap.Error(err))
		return nil, err
	}
	return rules, nil
}

// UpdateRule 更新规则
func (s *ruleService) UpdateRule(id string, rule *model.Rule) error {
	// 检查规则是否存在
	existingRule, err := s.ruleRepo.GetByID(id)
	if err != nil {
		s.logger.Error("获取规则失败", zap.Error(err), zap.String("rule_id", id))
		return err
	}

	// 更新字段
	existingRule.Name = rule.Name
	existingRule.Description = rule.Description
	existingRule.Category = rule.Category
	existingRule.TaskType = rule.TaskType
	existingRule.Condition = rule.Condition
	existingRule.ActionType = rule.ActionType
	existingRule.ActionConfig = rule.ActionConfig
	existingRule.Priority = rule.Priority
	existingRule.Enabled = rule.Enabled

	if err := s.validateRule(existingRule); err != nil {
		s.logger.Error("规则验证失败", zap.Error(err), zap.String("rule_id", id))
		return err
	}

	if err := s.ruleRepo.Update(existingRule); err != nil {
		s.logger.Error("更新规则失败", zap.Error(err), zap.String("rule_id", id))
		return err
	}

	s.logger.Info("成功更新规则", zap.String("rule_id", id), zap.String("rule_name", existingRule.Name))
	return nil
}

// DeleteRule 删除规则
func (s *ruleService) DeleteRule(id string) error {
	if err := s.ruleRepo.Delete(id); err != nil {
		s.logger.Error("删除规则失败", zap.Error(err), zap.String("rule_id", id))
		return err
	}

	s.logger.Info("成功删除规则", zap.String("rule_id", id))
	return nil
}

// EnableRule 启用规则
func (s *ruleService) EnableRule(id string) error {
	rule, err := s.ruleRepo.GetByID(id)
	if err != nil {
		s.logger.Error("获取规则失败", zap.Error(err), zap.String("rule_id", id))
		return err
	}

	rule.Enabled = true
	if err := s.ruleRepo.Update(rule); err != nil {
		s.logger.Error("启用规则失败", zap.Error(err), zap.String("rule_id", id))
		return err
	}

	s.logger.Info("成功启用规则", zap.String("rule_id", id))
	return nil
}

// DisableRule 禁用规则
func (s *ruleService) DisableRule(id string) error {
	rule, err := s.ruleRepo.GetByID(id)
	if err != nil {
		s.logger.Error("获取规则失败", zap.Error(err), zap.String("rule_id", id))
		return err
	}

	rule.Enabled = false
	if err := s.ruleRepo.Update(rule); err != nil {
		s.logger.Error("禁用规则失败", zap.Error(err), zap.String("rule_id", id))
		return err
	}

	s.logger.Info("成功禁用规则", zap.String("rule_id", id))
	return nil
}

// validateRule 验证规则
func (s *ruleService) validateRule(rule *model.Rule) error {
	if rule.ID == "" {
		return model.ErrInvalidInput("规则ID不能为空")
	}
	if rule.Name == "" {
		return model.ErrInvalidInput("规则名称不能为空")
	}
	if rule.Category == "" {
		return model.ErrInvalidInput("规则分类不能为空")
	}
	if rule.TaskType == "" {
		return model.ErrInvalidInput("任务类型不能为空")
	}
	if rule.Condition == "" {
		return model.ErrInvalidInput("规则条件不能为空")
	}
	if rule.ActionType == "" {
		return model.ErrInvalidInput("动作类型不能为空")
	}
	return nil
}
