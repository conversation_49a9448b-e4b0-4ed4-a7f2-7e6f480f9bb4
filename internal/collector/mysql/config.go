package mysql

import (
	"fmt"
)

// Config MySQL采集配置
type Config struct {
	DSN                     string               `json:"dsn"`                       // 数据库连接字符串
	Databases               []string             `json:"databases"`                 // 要监控的数据库列表
	Tables                  []TableConfig        `json:"tables"`                    // 要监控的表配置
	Metrics                 []string             `json:"metrics"`                   // 要采集的指标类型
	CustomQueries           []CustomQuery        `json:"custom_queries"`            // 自定义查询
	CrossDatabaseQueries    []CrossDatabaseQuery `json:"cross_database_queries"`    // 跨库查询
	Timeout                 string               `json:"timeout"`                   // 查询超时时间
	MaxOpenConns            int                  `json:"max_open_conns"`            // 最大打开连接数
	MaxIdleConns            int                  `json:"max_idle_conns"`            // 最大空闲连接数
	ConnMaxLifetime         int                  `json:"conn_max_lifetime"`         // 连接最大生存时间(秒)
	SlowQueryThreshold      string               `json:"slow_query_threshold"`      // 慢查询阈值
	EnablePerformanceSchema bool                 `json:"enable_performance_schema"` // 是否启用performance_schema
}

// TableConfig 表监控配置
type TableConfig struct {
	Database   string   `json:"database"`    // 数据库名
	Table      string   `json:"table"`       // 表名
	Metrics    []string `json:"metrics"`     // 要采集的表级指标
	CustomCols []string `json:"custom_cols"` // 自定义监控的列
}

// CustomQuery 自定义查询配置
type CustomQuery struct {
	Name         string                 `json:"name"`          // 查询名称
	Query        string                 `json:"query"`         // SQL查询语句
	Database     string                 `json:"database"`      // 目标数据库
	Interval     string                 `json:"interval"`      // 查询间隔
	MetricName   string                 `json:"metric_name"`   // 指标名称
	ValueColumn  string                 `json:"value_column"`  // 值列名
	LabelColumns []string               `json:"label_columns"` // 标签列名
	Tags         map[string]interface{} `json:"tags"`          // 额外标签
	Description  string                 `json:"description"`   // 查询描述
	Enabled      bool                   `json:"enabled"`       // 是否启用
}

// CrossDatabaseQuery 跨库查询配置
type CrossDatabaseQuery struct {
	Name         string         `json:"name"`          // 查询名称
	Query        string         `json:"query"`         // SQL查询语句(可以包含多个数据库)
	Databases    []string       `json:"databases"`     // 涉及的数据库列表
	MetricName   string         `json:"metric_name"`   // 指标名称
	ValueColumn  string         `json:"value_column"`  // 值列名
	LabelColumns []string       `json:"label_columns"` // 标签列名
	Tags         map[string]any `json:"tags"`          // 额外标签
	Description  string         `json:"description"`   // 查询描述
	Enabled      bool           `json:"enabled"`       // 是否启用
}

// ParseConfig 解析配置
func ParseConfig(config map[string]any) (*Config, error) {
	mysqlConfig := &Config{
		Metrics:                 []string{"status", "connections", "queries", "innodb"},
		Databases:               []string{},
		Tables:                  []TableConfig{},
		CustomQueries:           []CustomQuery{},
		CrossDatabaseQueries:    []CrossDatabaseQuery{},
		Timeout:                 "30s",
		MaxOpenConns:            10,
		MaxIdleConns:            5,
		ConnMaxLifetime:         300,
		SlowQueryThreshold:      "1s",
		EnablePerformanceSchema: false,
	}

	// 解析DSN
	if dsn, ok := config["dsn"].(string); ok {
		mysqlConfig.DSN = dsn
	} else {
		return nil, fmt.Errorf("MySQL DSN 未配置")
	}

	// 解析要采集的指标类型
	if metrics, ok := config["metrics"].([]interface{}); ok {
		for _, metric := range metrics {
			if metricStr, ok := metric.(string); ok {
				mysqlConfig.Metrics = append(mysqlConfig.Metrics, metricStr)
			}
		}
	}

	// 解析数据库列表
	if databases, ok := config["databases"].([]interface{}); ok {
		for _, db := range databases {
			if dbName, ok := db.(string); ok {
				mysqlConfig.Databases = append(mysqlConfig.Databases, dbName)
			}
		}
	}

	// 解析表配置
	if tablesData, ok := config["tables"].([]interface{}); ok {
		for _, tableData := range tablesData {
			if tableMap, ok := tableData.(map[string]interface{}); ok {
				tableConfig := TableConfig{}

				if database, ok := tableMap["database"].(string); ok {
					tableConfig.Database = database
				}
				if table, ok := tableMap["table"].(string); ok {
					tableConfig.Table = table
				}
				if metrics, ok := tableMap["metrics"].([]interface{}); ok {
					for _, metric := range metrics {
						if metricStr, ok := metric.(string); ok {
							tableConfig.Metrics = append(tableConfig.Metrics, metricStr)
						}
					}
				}
				if customCols, ok := tableMap["custom_cols"].([]interface{}); ok {
					for _, col := range customCols {
						if colStr, ok := col.(string); ok {
							tableConfig.CustomCols = append(tableConfig.CustomCols, colStr)
						}
					}
				}

				mysqlConfig.Tables = append(mysqlConfig.Tables, tableConfig)
			}
		}
	}

	// 解析连接池配置
	if maxOpenConns, ok := config["max_open_conns"].(float64); ok {
		mysqlConfig.MaxOpenConns = int(maxOpenConns)
	}
	if maxIdleConns, ok := config["max_idle_conns"].(float64); ok {
		mysqlConfig.MaxIdleConns = int(maxIdleConns)
	}
	if connMaxLifetime, ok := config["conn_max_lifetime"].(float64); ok {
		mysqlConfig.ConnMaxLifetime = int(connMaxLifetime)
	}

	// 解析其他配置
	if timeout, ok := config["timeout"].(string); ok {
		mysqlConfig.Timeout = timeout
	}
	if slowQueryThreshold, ok := config["slow_query_threshold"].(string); ok {
		mysqlConfig.SlowQueryThreshold = slowQueryThreshold
	}
	if enablePerfSchema, ok := config["enable_performance_schema"].(bool); ok {
		mysqlConfig.EnablePerformanceSchema = enablePerfSchema
	}

	// 解析自定义查询
	if customQueriesData, ok := config["custom_queries"].([]interface{}); ok {
		for _, queryData := range customQueriesData {
			if queryMap, ok := queryData.(map[string]interface{}); ok {
				customQuery := CustomQuery{Enabled: true} // 默认启用

				if name, ok := queryMap["name"].(string); ok {
					customQuery.Name = name
				}
				if query, ok := queryMap["query"].(string); ok {
					customQuery.Query = query
				}
				if database, ok := queryMap["database"].(string); ok {
					customQuery.Database = database
				}
				if interval, ok := queryMap["interval"].(string); ok {
					customQuery.Interval = interval
				}
				if metricName, ok := queryMap["metric_name"].(string); ok {
					customQuery.MetricName = metricName
				}
				if valueColumn, ok := queryMap["value_column"].(string); ok {
					customQuery.ValueColumn = valueColumn
				}
				if description, ok := queryMap["description"].(string); ok {
					customQuery.Description = description
				}
				if enabled, ok := queryMap["enabled"].(bool); ok {
					customQuery.Enabled = enabled
				}
				if labelColumns, ok := queryMap["label_columns"].([]interface{}); ok {
					for _, col := range labelColumns {
						if colName, ok := col.(string); ok {
							customQuery.LabelColumns = append(customQuery.LabelColumns, colName)
						}
					}
				}
				if tags, ok := queryMap["tags"].(map[string]interface{}); ok {
					customQuery.Tags = tags
				}

				if customQuery.Enabled {
					mysqlConfig.CustomQueries = append(mysqlConfig.CustomQueries, customQuery)
				}
			}
		}
	}

	// 解析跨库查询
	if crossQueriesData, ok := config["cross_database_queries"].([]interface{}); ok {
		for _, queryData := range crossQueriesData {
			if queryMap, ok := queryData.(map[string]interface{}); ok {
				crossQuery := CrossDatabaseQuery{Enabled: true} // 默认启用

				if name, ok := queryMap["name"].(string); ok {
					crossQuery.Name = name
				}
				if query, ok := queryMap["query"].(string); ok {
					crossQuery.Query = query
				}
				if metricName, ok := queryMap["metric_name"].(string); ok {
					crossQuery.MetricName = metricName
				}
				if valueColumn, ok := queryMap["value_column"].(string); ok {
					crossQuery.ValueColumn = valueColumn
				}
				if description, ok := queryMap["description"].(string); ok {
					crossQuery.Description = description
				}
				if enabled, ok := queryMap["enabled"].(bool); ok {
					crossQuery.Enabled = enabled
				}
				if databases, ok := queryMap["databases"].([]interface{}); ok {
					for _, db := range databases {
						if dbName, ok := db.(string); ok {
							crossQuery.Databases = append(crossQuery.Databases, dbName)
						}
					}
				}
				if labelColumns, ok := queryMap["label_columns"].([]interface{}); ok {
					for _, col := range labelColumns {
						if colName, ok := col.(string); ok {
							crossQuery.LabelColumns = append(crossQuery.LabelColumns, colName)
						}
					}
				}
				if tags, ok := queryMap["tags"].(map[string]interface{}); ok {
					crossQuery.Tags = tags
				}

				if crossQuery.Enabled {
					mysqlConfig.CrossDatabaseQueries = append(mysqlConfig.CrossDatabaseQueries, crossQuery)
				}
			}
		}
	}

	return mysqlConfig, nil
}
