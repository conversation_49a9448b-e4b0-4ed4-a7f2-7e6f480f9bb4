package controller

import (
	"aiops/internal/common"
	"aiops/internal/model"
	"aiops/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RuleController 规则控制器
type RuleController struct {
	ruleService service.RuleService
	logger      *zap.Logger
}

// NewRuleController 创建规则控制器实例
func NewRuleController(ruleService service.RuleService, logger *zap.Logger) *RuleController {
	return &RuleController{
		ruleService: ruleService,
		logger:      logger,
	}
}

// CreateRule 创建规则
func (c *RuleController) CreateRule(ctx *gin.Context) {
	var rule model.Rule
	if err := ctx.ShouldBindJSON(&rule); err != nil {
		c.logger.Error("绑定规则数据失败", zap.Error(err))
		common.HandleError(ctx, common.BadRequest, err)
		return
	}

	if err := c.ruleService.CreateRule(&rule); err != nil {
		c.logger.Error("创建规则失败", zap.Error(err))
		common.HandleError(ctx, common.InternalServerError, err)
		return
	}

	common.HandleSuccess(ctx, gin.H{"rule": rule})
}

// GetRules 获取规则列表
func (c *RuleController) GetRules(ctx *gin.Context) {
	category := ctx.Query("category")
	taskType := ctx.Query("task_type")
	onlyEnabled := ctx.Query("enabled")

	var rules []*model.Rule
	var err error

	if onlyEnabled == "true" {
		rules, err = c.ruleService.GetEnabledRules()
	} else {
		var ruleCategory model.RuleCategory
		if category != "" {
			ruleCategory = model.RuleCategory(category)
		}
		rules, err = c.ruleService.GetRules(ruleCategory, taskType)
	}

	if err != nil {
		c.logger.Error("获取规则列表失败", zap.Error(err))
		common.HandleError(ctx, common.InternalServerError, err)
		return
	}

	common.HandleSuccess(ctx, gin.H{
		"rules": rules,
		"total": len(rules),
	})
}

// GetRule 获取单个规则
func (c *RuleController) GetRule(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		common.HandleError(ctx, common.BadRequest, model.ErrInvalidInput("规则ID不能为空"))
		return
	}

	rule, err := c.ruleService.GetRule(id)
	if err != nil {
		c.logger.Error("获取规则失败", zap.Error(err), zap.String("rule_id", id))
		common.HandleError(ctx, common.NotFound, err)
		return
	}

	common.HandleSuccess(ctx, gin.H{"rule": rule})
}

// UpdateRule 更新规则
func (c *RuleController) UpdateRule(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		common.HandleError(ctx, common.BadRequest, model.ErrInvalidInput("规则ID不能为空"))
		return
	}

	var rule model.Rule
	if err := ctx.ShouldBindJSON(&rule); err != nil {
		c.logger.Error("绑定规则数据失败", zap.Error(err))
		common.HandleError(ctx, common.BadRequest, err)
		return
	}

	if err := c.ruleService.UpdateRule(id, &rule); err != nil {
		c.logger.Error("更新规则失败", zap.Error(err), zap.String("rule_id", id))
		common.HandleError(ctx, common.InternalServerError, err)
		return
	}

	common.HandleSuccess(ctx, gin.H{"message": "规则更新成功"})
}

// DeleteRule 删除规则
func (c *RuleController) DeleteRule(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		common.HandleError(ctx, common.BadRequest, model.ErrInvalidInput("规则ID不能为空"))
		return
	}

	if err := c.ruleService.DeleteRule(id); err != nil {
		c.logger.Error("删除规则失败", zap.Error(err), zap.String("rule_id", id))
		common.HandleError(ctx, common.InternalServerError, err)
		return
	}

	common.HandleSuccess(ctx, gin.H{"message": "规则删除成功"})
}

// EnableRule 启用规则
func (c *RuleController) EnableRule(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		common.HandleError(ctx, common.BadRequest, model.ErrInvalidInput("规则ID不能为空"))
		return
	}

	if err := c.ruleService.EnableRule(id); err != nil {
		c.logger.Error("启用规则失败", zap.Error(err), zap.String("rule_id", id))
		common.HandleError(ctx, common.InternalServerError, err)
		return
	}

	common.HandleSuccess(ctx, gin.H{"message": "规则启用成功"})
}

// DisableRule 禁用规则
func (c *RuleController) DisableRule(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		common.HandleError(ctx, common.BadRequest, model.ErrInvalidInput("规则ID不能为空"))
		return
	}

	if err := c.ruleService.DisableRule(id); err != nil {
		c.logger.Error("禁用规则失败", zap.Error(err), zap.String("rule_id", id))
		common.HandleError(ctx, common.InternalServerError, err)
		return
	}

	common.HandleSuccess(ctx, gin.H{"message": "规则禁用成功"})
}

// GetRuleCategories 获取规则分类列表
func (c *RuleController) GetRuleCategories(ctx *gin.Context) {
	categories := []string{
		string(model.RuleCategoryThreshold),
		string(model.RuleCategoryAnomaly),
		string(model.RuleCategoryPerformance),
		string(model.RuleCategorySystem),
		string(model.RuleCategoryCustom),
	}

	common.HandleSuccess(ctx, gin.H{"categories": categories})
}

// GetActionTypes 获取动作类型列表
func (c *RuleController) GetActionTypes(ctx *gin.Context) {
	actionTypes := []string{
		string(model.ActionTypeAlert),
		string(model.ActionTypeEmail),
		string(model.ActionTypeWebhook),
		string(model.ActionTypeLog),
		string(model.ActionTypeScript),
	}

	common.HandleSuccess(ctx, gin.H{"action_types": actionTypes})
}
