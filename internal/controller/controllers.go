package controller

import (
	"aiops/internal/service"

	"go.uber.org/zap"
)

// Controllers 控制器集合
type Controllers struct {
	Task   *TaskController
	Metric *MetricController
	Alert  *AlertController
	Log    *LogController
	Rule   *RuleController
	Health *HealthController
}

// NewControllers 创建控制器实例
func NewControllers(services *service.Services, scheduler Scheduler, logger *zap.Logger) *Controllers {
	return &Controllers{
		Task:   NewTaskController(services.Task, scheduler, logger),
		Metric: NewMetricController(services.Metric, logger),
		Alert:  NewAlertController(services.Alert, logger),
		Log:    NewLogController(services.Log, logger),
		Rule:   NewRuleController(services.Rule, logger),
		Health: NewHealthController(),
	}
}
