package analyzer

import (
	"context"
	"fmt"
	"math"
	"sort"
	"time"

	"aiops/internal/model"
	"aiops/internal/repository"

	"go.uber.org/zap"
)

// AnomalyDetector 异常检测分析器
type AnomalyDetector struct {
	repos  *repository.Repositories
	logger *zap.Logger
}

// NewAnomalyDetector 创建异常检测分析器
func NewAnomalyDetector(repos *repository.Repositories, logger *zap.Logger) *AnomalyDetector {
	return &AnomalyDetector{
		repos:  repos,
		logger: logger,
	}
}

// Analyze 执行异常检测分析
func (a *AnomalyDetector) Analyze(ctx context.Context, taskID string, config map[string]interface{}) (*AnalysisResult, error) {
	// 获取配置参数
	metricName, ok := config["metric"].(string)
	if !ok {
		return nil, fmt.Errorf("指标名称未配置")
	}

	algorithm, ok := config["algorithm"].(string)
	if !ok {
		algorithm = "statistical" // 默认使用统计方法
	}

	windowStr, ok := config["window"].(string)
	if !ok {
		windowStr = "1h" // 默认1小时窗口
	}

	window, err := time.ParseDuration(windowStr)
	if err != nil {
		return nil, fmt.Errorf("无效的时间窗口: %w", err)
	}

	// 获取历史数据
	end := time.Now()
	start := end.Add(-window)

	metrics, err := a.repos.Metric.GetByRange(taskID, metricName, start, end)
	if err != nil {
		return nil, fmt.Errorf("获取历史数据失败: %w", err)
	}

	if len(metrics) == 0 {
		return &AnalysisResult{
			TaskID:    taskID,
			Type:      "anomaly_detection",
			Status:    "no_data",
			Message:   "没有足够的历史数据进行异常检测",
			Timestamp: time.Now(),
		}, nil
	}

	// 根据算法执行异常检测
	var result *AnalysisResult
	switch algorithm {
	case "statistical":
		result, err = a.statisticalAnomalyDetection(taskID, metricName, metrics, config)
	case "iqr":
		result, err = a.iqrAnomalyDetection(taskID, metricName, metrics, config)
	case "zscore":
		result, err = a.zscoreAnomalyDetection(taskID, metricName, metrics, config)
	default:
		return nil, fmt.Errorf("不支持的异常检测算法: %s", algorithm)
	}

	if err != nil {
		return nil, err
	}

	a.logger.Info("异常检测分析完成",
		zap.String("task_id", taskID),
		zap.String("metric", metricName),
		zap.String("algorithm", algorithm),
		zap.String("status", result.Status))

	return result, nil
}

// statisticalAnomalyDetection 统计异常检测
func (a *AnomalyDetector) statisticalAnomalyDetection(taskID, metricName string, metrics []*model.MetricData, config map[string]interface{}) (*AnalysisResult, error) {
	// 提取数值
	values := make([]float64, len(metrics))
	for i, metric := range metrics {
		values[i] = metric.Value
	}

	// 计算统计指标
	mean := calculateMean(values)
	stdDev := calculateStdDev(values, mean)

	// 获取阈值配置
	threshold := 2.0 // 默认2个标准差
	if t, ok := config["threshold"].(float64); ok {
		threshold = t
	}

	// 检测异常
	latest := values[len(values)-1]
	isAnomaly := math.Abs(latest-mean) > threshold*stdDev

	result := &AnalysisResult{
		TaskID:    taskID,
		Type:      "anomaly_detection",
		Algorithm: "statistical",
		Timestamp: time.Now(),
		Details: map[string]any{
			"metric_name":   metricName,
			"current_value": latest,
			"mean":          mean,
			"std_dev":       stdDev,
			"threshold":     threshold,
			"deviation":     math.Abs(latest-mean) / stdDev,
			"sample_count":  len(values),
		},
	}

	if isAnomaly {
		result.Status = "anomaly_detected"
		result.Message = fmt.Sprintf("检测到异常: 当前值 %.2f 偏离均值 %.2f 超过 %.1f 个标准差", latest, mean, threshold)
		result.Severity = "warning"
		if math.Abs(latest-mean) > 3*stdDev {
			result.Severity = "critical"
		}
	} else {
		result.Status = "normal"
		result.Message = "未检测到异常"
		result.Severity = "info"
	}

	return result, nil
}

// iqrAnomalyDetection IQR 异常检测
func (a *AnomalyDetector) iqrAnomalyDetection(taskID, metricName string, metrics []*model.MetricData, config map[string]interface{}) (*AnalysisResult, error) {
	// 提取数值并排序
	values := make([]float64, len(metrics))
	for i, metric := range metrics {
		values[i] = metric.Value
	}
	sort.Float64s(values)

	// 计算四分位数
	q1 := calculatePercentile(values, 25)
	q3 := calculatePercentile(values, 75)
	iqr := q3 - q1

	// IQR 异常检测阈值
	multiplier := 1.5 // 默认1.5倍IQR
	if m, ok := config["iqr_multiplier"].(float64); ok {
		multiplier = m
	}

	lowerBound := q1 - multiplier*iqr
	upperBound := q3 + multiplier*iqr

	// 检测异常
	latest := values[len(values)-1]
	isAnomaly := latest < lowerBound || latest > upperBound

	result := &AnalysisResult{
		TaskID:    taskID,
		Type:      "anomaly_detection",
		Algorithm: "iqr",
		Timestamp: time.Now(),
		Details: map[string]interface{}{
			"metric_name":   metricName,
			"current_value": latest,
			"q1":            q1,
			"q3":            q3,
			"iqr":           iqr,
			"lower_bound":   lowerBound,
			"upper_bound":   upperBound,
			"multiplier":    multiplier,
			"sample_count":  len(values),
		},
	}

	if isAnomaly {
		result.Status = "anomaly_detected"
		if latest < lowerBound {
			result.Message = fmt.Sprintf("检测到异常: 当前值 %.2f 低于下界 %.2f", latest, lowerBound)
		} else {
			result.Message = fmt.Sprintf("检测到异常: 当前值 %.2f 高于上界 %.2f", latest, upperBound)
		}
		result.Severity = "warning"
	} else {
		result.Status = "normal"
		result.Message = "未检测到异常"
		result.Severity = "info"
	}

	return result, nil
}

// zscoreAnomalyDetection Z-Score 异常检测
func (a *AnomalyDetector) zscoreAnomalyDetection(taskID, metricName string, metrics []*model.MetricData, config map[string]interface{}) (*AnalysisResult, error) {
	// 提取数值
	values := make([]float64, len(metrics))
	for i, metric := range metrics {
		values[i] = metric.Value
	}

	// 计算统计指标
	mean := calculateMean(values)
	stdDev := calculateStdDev(values, mean)

	if stdDev == 0 {
		return &AnalysisResult{
			TaskID:    taskID,
			Type:      "anomaly_detection",
			Algorithm: "zscore",
			Status:    "no_variance",
			Message:   "数据无变化，无法进行Z-Score异常检测",
			Timestamp: time.Now(),
		}, nil
	}

	// 计算Z-Score
	latest := values[len(values)-1]
	zscore := (latest - mean) / stdDev

	// 获取阈值配置
	threshold := 2.0 // 默认Z-Score阈值
	if t, ok := config["zscore_threshold"].(float64); ok {
		threshold = t
	}

	// 检测异常
	isAnomaly := math.Abs(zscore) > threshold

	result := &AnalysisResult{
		TaskID:    taskID,
		Type:      "anomaly_detection",
		Algorithm: "zscore",
		Timestamp: time.Now(),
		Details: map[string]interface{}{
			"metric_name":   metricName,
			"current_value": latest,
			"mean":          mean,
			"std_dev":       stdDev,
			"zscore":        zscore,
			"threshold":     threshold,
			"sample_count":  len(values),
		},
	}

	if isAnomaly {
		result.Status = "anomaly_detected"
		result.Message = fmt.Sprintf("检测到异常: Z-Score %.2f 超过阈值 %.1f", zscore, threshold)
		result.Severity = "warning"
		if math.Abs(zscore) > 3.0 {
			result.Severity = "critical"
		}
	} else {
		result.Status = "normal"
		result.Message = "未检测到异常"
		result.Severity = "info"
	}

	return result, nil
}

// 辅助函数
func calculateMean(values []float64) float64 {
	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

func calculateStdDev(values []float64, mean float64) float64 {
	sum := 0.0
	for _, v := range values {
		sum += math.Pow(v-mean, 2)
	}
	return math.Sqrt(sum / float64(len(values)))
}

func calculatePercentile(sortedValues []float64, percentile float64) float64 {
	index := percentile / 100.0 * float64(len(sortedValues)-1)
	lower := int(math.Floor(index))
	upper := int(math.Ceil(index))

	if lower == upper {
		return sortedValues[lower]
	}

	weight := index - float64(lower)
	return sortedValues[lower]*(1-weight) + sortedValues[upper]*weight
}
