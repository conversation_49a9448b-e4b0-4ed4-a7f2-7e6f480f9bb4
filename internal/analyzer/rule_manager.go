package analyzer

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"aiops/internal/model"
	"aiops/pkg/rules"
	"aiops/pkg/rules/action"
	"aiops/pkg/rules/condition"
	"aiops/pkg/rules/core"
	"aiops/pkg/rules/dsl"
)

// RuleManager 管理和执行规则
type RuleManager struct {
	engine     core.RuleEngine
	rules      map[string]core.Rule
	rulesMutex sync.RWMutex

	// 配置
	config RuleManagerConfig
	// 停止信号
	stopChan chan struct{}
}

// RuleManagerConfig 规则管理器配置
type RuleManagerConfig struct {
	// 规则重载间隔
	ReloadInterval time.Duration

	// 是否启用规则优化
	EnableOptimization bool

	// 最大并发规则执行数
	MaxConcurrentRules int

	// 规则执行超时
	RuleExecutionTimeout time.Duration
}

// DefaultRuleManagerConfig 默认配置
func DefaultRuleManagerConfig() RuleManagerConfig {
	return RuleManagerConfig{
		ReloadInterval:       5 * time.Minute,
		EnableOptimization:   true,
		MaxConcurrentRules:   10,
		RuleExecutionTimeout: 30 * time.Second,
	}
}

// NewRuleManager 创建新的规则管理器
func NewRuleManager(config RuleManagerConfig) (*RuleManager, error) {
	// 创建规则引擎
	engine := rules.CreateEngine(true, config.MaxConcurrentRules)
	rm := &RuleManager{
		engine:   engine,
		rules:    make(map[string]core.Rule),
		config:   config,
		stopChan: make(chan struct{}),
	}
	return rm, nil
}

// LoadRules 从数据库加载规则
func (rm *RuleManager) LoadRules() error {
	// 从数据库获取所有活跃规则
	dbRules, err := rm.getActiveRulesFromDB()
	if err != nil {
		return fmt.Errorf("failed to get rules from database: %w", err)
	}

	rm.rulesMutex.Lock()
	defer rm.rulesMutex.Unlock()

	// 清空现有规则
	rm.rules = make(map[string]core.Rule)

	// 转换并加载规则
	var engineRules []core.Rule
	for _, dbRule := range dbRules {
		engineRule, err := rm.convertDBRuleToEngineRule(dbRule)
		if err != nil {
			log.Printf("Failed to convert rule %s: %v", dbRule.Name, err)
			continue
		}

		// 保存到本地映射
		rm.rules[dbRule.Name] = engineRule
		engineRules = append(engineRules, engineRule)
	}

	// 批量添加到引擎
	if err := rm.engine.AddRules(engineRules); err != nil {
		return fmt.Errorf("failed to add rules to engine: %w", err)
	}

	log.Printf("Loaded %d rules successfully", len(rm.rules))
	return nil
}

// EvaluateRules 评估规则
func (rm *RuleManager) EvaluateRules(ctx context.Context, data map[string]interface{}) error {
	// 使用规则引擎评估
	result, err := rm.engine.Execute(ctx, data)
	if err != nil {
		return fmt.Errorf("failed to execute rules: %w", err)
	}

	// 处理评估结果
	for _, matchedRule := range result.Matches {
		log.Printf("Rule %s matched", matchedRule.Rule.GetID())

		// 处理动作执行结果
		for _, actionResult := range matchedRule.ActionResults {
			if actionResult.Error != nil {
				log.Printf("Action error: %v", actionResult.Error)
			}
		}
	}

	return nil
}

// ExecuteRules 执行规则并返回结果
func (rm *RuleManager) ExecuteRules(ctx context.Context, data map[string]interface{}) ([]RuleExecutionResult, error) {
	// 使用规则引擎评估
	result, err := rm.engine.Execute(ctx, data)
	if err != nil {
		return nil, fmt.Errorf("failed to execute rules: %w", err)
	}

	// 转换为规则执行结果
	var results []RuleExecutionResult
	for _, matchedRule := range result.Matches {
		ruleResult := RuleExecutionResult{
			RuleID:   matchedRule.Rule.GetID(),
			RuleName: matchedRule.Rule.GetName(),
			Matched:  true,
			Score:    1.0, // 基础匹配得分
			Message:  fmt.Sprintf("Rule %s matched", matchedRule.Rule.GetName()),
		}

		// 如果有错误，设置错误信息
		for _, actionResult := range matchedRule.ActionResults {
			if actionResult.Error != nil {
				ruleResult.Message += fmt.Sprintf("; Action error: %v", actionResult.Error)
				ruleResult.Score = 0.5 // 降低得分因为有错误
			}
		}

		results = append(results, ruleResult)
	}
	// 处理错误
	if result.HasErrors() {
		for _, ruleErr := range result.GetErrors() {
			log.Printf("Rule execution error: %v", ruleErr)
		}
	}
	return results, nil
}

// RuleExecutionResult 规则执行结果
type RuleExecutionResult struct {
	RuleID   string  `json:"rule_id"`
	RuleName string  `json:"rule_name"`
	Matched  bool    `json:"matched"`
	Score    float64 `json:"score"`
	Message  string  `json:"message"`
}

// GetRule 获取指定规则
func (rm *RuleManager) GetRule(name string) (core.Rule, bool) {
	rm.rulesMutex.RLock()
	defer rm.rulesMutex.RUnlock()

	rule, exists := rm.rules[name]
	return rule, exists
}

// GetAllRules 获取所有规则
func (rm *RuleManager) GetAllRules() map[string]core.Rule {
	rm.rulesMutex.RLock()
	defer rm.rulesMutex.RUnlock()

	result := make(map[string]core.Rule)
	for k, v := range rm.rules {
		result[k] = v
	}
	return result
}

// AddRule 添加新规则
func (rm *RuleManager) AddRule(rule *model.Rule) error {
	// 转换为引擎规则
	engineRule, err := rm.convertDBRuleToEngineRule(rule)
	if err != nil {
		return fmt.Errorf("failed to convert rule: %w", err)
	}

	// 添加到引擎
	if err := rm.engine.AddRule(engineRule); err != nil {
		return fmt.Errorf("failed to add rule to engine: %w", err)
	}

	// 更新本地映射
	rm.rulesMutex.Lock()
	rm.rules[rule.Name] = engineRule
	rm.rulesMutex.Unlock()

	log.Printf("Added rule %s successfully", rule.Name)
	return nil
}

// RemoveRule 移除规则
func (rm *RuleManager) RemoveRule(name string) error {
	// 从引擎移除
	if err := rm.engine.RemoveRule(name); err != nil {
		return fmt.Errorf("failed to remove rule from engine: %w", err)
	}

	// 从本地映射移除
	rm.rulesMutex.Lock()
	delete(rm.rules, name)
	rm.rulesMutex.Unlock()

	log.Printf("Removed rule %s successfully", name)
	return nil
}

// convertDBRuleToEngineRule 将数据库规则转换为引擎规则
func (rm *RuleManager) convertDBRuleToEngineRule(dbRule *model.Rule) (core.Rule, error) {
	// 解析条件
	ruleCondition, err := rm.parseCondition(dbRule)
	if err != nil {
		return nil, fmt.Errorf("failed to parse condition: %w", err)
	}

	// 解析动作
	ruleActions, err := rm.parseActions(dbRule)
	if err != nil {
		return nil, fmt.Errorf("failed to parse actions: %w", err)
	}

	// 使用core.NewRule创建规则
	engineRule := core.NewRule(
		dbRule.Name,
		dbRule.Description,
		dbRule.Priority,
		ruleCondition,
		ruleActions,
		dbRule.Status == model.RuleStatusEnabled,
	)

	return engineRule, nil
}

// parseCondition 解析条件
func (rm *RuleManager) parseCondition(dbRule *model.Rule) (core.Condition, error) {
	// 优先使用 DSL 条件
	if len(dbRule.ConditionDSL) > 0 {
		return rm.parseDSLCondition(dbRule.ConditionDSL)
	}
	// 回退到简单条件
	if dbRule.Condition != "" {
		return rm.parseSimpleCondition(dbRule.Condition)
	}

	return nil, fmt.Errorf("no condition defined for rule %s", dbRule.Name)
}

// parseDSLCondition 解析DSL条件
func (rm *RuleManager) parseDSLCondition(dslData json.RawMessage) (core.Condition, error) {
	// 将 json.RawMessage 转换为字符串
	dslString := string(dslData)

	// 使用DSL系统直接从字符串创建条件
	dslCondition, err := dsl.DSLConditionFromString(dslString)
	if err != nil {
		return nil, fmt.Errorf("failed to create DSL condition: %w", err)
	}

	// DSLCondition 实现了 core.Condition 接口，可以直接返回
	return dslCondition, nil
}

// parseSimpleCondition 解析简单条件（向后兼容）
func (rm *RuleManager) parseSimpleCondition(conditionStr string) (core.Condition, error) {
	// 对于简单的字符串条件，创建一个基本的相等条件
	// 这主要用于向后兼容旧的规则格式
	// 实际项目中可以根据需要实现更复杂的解析逻辑
	return condition.NewSimpleCondition("status", condition.Operator("eq"), conditionStr), nil
}

// parseActions 解析动作
func (rm *RuleManager) parseActions(dbRule *model.Rule) ([]core.Action, error) {
	var actions []core.Action

	// 优先使用新的动作格式
	if len(dbRule.Actions) > 0 {
		return rm.parseActionsFromJSON(dbRule.Actions)
	}

	// 回退到旧的单个动作格式
	if len(dbRule.ActionConfig) > 0 {
		action, err := rm.parseActionFromConfig(dbRule.ActionType, dbRule.ActionConfig)
		if err != nil {
			return nil, err
		}
		actions = append(actions, action)
	}

	// 如果都没有，创建一个默认的日志动作
	if len(actions) == 0 {
		logAction := action.NewLogAction("Rule matched", "info", "")
		actions = append(actions, logAction)
	}

	return actions, nil
}

// parseActionsFromJSON 从JSON解析动作列表
func (rm *RuleManager) parseActionsFromJSON(actionsData json.RawMessage) ([]core.Action, error) {
	var actionDefs []model.ActionDefinition
	if err := json.Unmarshal(actionsData, &actionDefs); err != nil {
		return nil, fmt.Errorf("failed to unmarshal actions: %w", err)
	}

	var actions []core.Action
	for _, actionDef := range actionDefs {
		engineAction, err := rm.convertActionDefToAction(actionDef)
		if err != nil {
			log.Printf("Failed to convert action %s: %v", actionDef.Type, err)
			continue
		}
		actions = append(actions, engineAction)
	}

	return actions, nil
}

// parseActionFromConfig 从配置解析单个动作
func (rm *RuleManager) parseActionFromConfig(actionType model.ActionType, configData json.RawMessage) (core.Action, error) {
	actionDef := model.ActionDefinition{
		Type: string(actionType),
	}

	if len(configData) > 0 {
		if err := json.Unmarshal(configData, &actionDef.Config); err != nil {
			return nil, fmt.Errorf("failed to unmarshal action config: %w", err)
		}
	}

	return rm.convertActionDefToAction(actionDef)
}

// convertActionDefToAction 将动作定义转换为引擎动作
func (rm *RuleManager) convertActionDefToAction(actionDef model.ActionDefinition) (core.Action, error) {
	switch actionDef.Type {
	case "log":
		return rm.createLogAction(actionDef.Config)
	case "alert":
		return rm.createAlertAction(actionDef.Config)
	case "scale":
		return rm.createScaleAction(actionDef.Config)
	case "restart":
		return rm.createRestartAction(actionDef.Config)
	case "modify":
		return rm.createModifyAction(actionDef.Config)
	default:
		return nil, fmt.Errorf("unsupported action type: %s", actionDef.Type)
	}
}

// createLogAction 创建日志动作
func (rm *RuleManager) createLogAction(config map[string]any) (core.Action, error) {
	level, _ := config["level"].(string)
	if level == "" {
		level = "info"
	}

	category, _ := config["category"].(string)
	if category == "" {
		category = "rule"
	}

	message, _ := config["message"].(string)
	if message == "" {
		message = "Rule action executed"
	}

	// 格式化消息以包含类别信息
	formattedMessage := fmt.Sprintf("[%s] %s", category, message)

	return action.NewLogAction(formattedMessage, level, ""), nil
}

// createAlertAction 创建告警动作
func (rm *RuleManager) createAlertAction(config map[string]any) (core.Action, error) {
	// 这里应该创建告警动作
	// 暂时使用日志动作代替

	return rm.createLogAction(map[string]any{
		"level":    "warn",
		"category": "alert",
		"message":  "Alert triggered",
	})
}

// createScaleAction 创建扩缩容动作
func (rm *RuleManager) createScaleAction(config map[string]any) (core.Action, error) {
	// 这里应该创建扩缩容动作
	// 暂时使用日志动作代替
	return rm.createLogAction(map[string]any{
		"level":    "info",
		"category": "scale",
		"message":  "Scale action triggered",
	})
}

// createRestartAction 创建重启动作
func (rm *RuleManager) createRestartAction(config map[string]any) (core.Action, error) {
	// 这里应该创建重启动作
	// 暂时使用日志动作代替
	return rm.createLogAction(map[string]any{
		"level":    "info",
		"category": "restart",
		"message":  "Restart action triggered",
	})
}

// createModifyAction 创建修改动作
func (rm *RuleManager) createModifyAction(config map[string]any) (core.Action, error) {
	property, _ := config["property"].(string)
	value := config["value"]

	return action.NewSetPropertyAction(property, value), nil
}

// getActiveRulesFromDB 从数据库获取活跃规则
func (rm *RuleManager) getActiveRulesFromDB() ([]*model.Rule, error) {
	// 这里应该调用数据库查询方法
	// 暂时返回空切片，需要根据实际数据库接口实现

	// 示例查询逻辑：
	// return rm.db.GetActiveRules()

	return []*model.Rule{}, nil
}

// GetStats 获取规则管理器统计信息
func (rm *RuleManager) GetStats() map[string]interface{} {
	rm.rulesMutex.RLock()
	totalRules := len(rm.rules)
	rm.rulesMutex.RUnlock()

	return map[string]any{
		"total_rules":       totalRules,
		"engine_enabled":    rm.engine != nil,
		"optimization":      rm.config.EnableOptimization,
		"reload_interval":   rm.config.ReloadInterval.String(),
		"execution_timeout": rm.config.RuleExecutionTimeout.String(),
		"max_concurrent":    rm.config.MaxConcurrentRules,
	}
}
