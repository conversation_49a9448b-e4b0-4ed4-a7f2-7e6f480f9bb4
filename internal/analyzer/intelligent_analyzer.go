package analyzer

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops/internal/model"
	"aiops/internal/repository"
	"aiops/pkg/rules"

	"maps"

	"go.uber.org/zap"
)

// ResultCache 分析结果缓存
type ResultCache struct {
	cache map[string]*AnalysisResult
	mu    sync.RWMutex
	size  int
	ttl   time.Duration
}

// NewResultCache 创建结果缓存
func NewResultCache(size int, ttl time.Duration) *ResultCache {
	return &ResultCache{
		cache: make(map[string]*AnalysisResult),
		size:  size,
		ttl:   ttl,
	}
}

// Get 获取缓存结果
func (rc *ResultCache) Get(taskID string, config map[string]interface{}) *AnalysisResult {
	rc.mu.RLock()
	defer rc.mu.RUnlock()

	key := rc.generateKey(taskID, config)
	if result, exists := rc.cache[key]; exists {
		if time.Since(result.Timestamp) < rc.ttl {
			return result
		}
		// 过期删除
		delete(rc.cache, key)
	}
	return nil
}

// Set 设置缓存结果
func (rc *ResultCache) Set(taskID string, config map[string]interface{}, result *AnalysisResult) {
	rc.mu.Lock()
	defer rc.mu.Unlock()

	key := rc.generateKey(taskID, config)
	rc.cache[key] = result

	// 简单的LRU清理
	if len(rc.cache) > rc.size {
		// 删除最旧的条目
		var oldestKey string
		var oldestTime time.Time
		for k, v := range rc.cache {
			if oldestKey == "" || v.Timestamp.Before(oldestTime) {
				oldestKey = k
				oldestTime = v.Timestamp
			}
		}
		delete(rc.cache, oldestKey)
	}
}

// generateKey 生成缓存键
func (rc *ResultCache) generateKey(taskID string, config map[string]interface{}) string {
	return fmt.Sprintf("%s:%v", taskID, config)
}

// AnalysisStatsCollector 分析统计收集器
type AnalysisStatsCollector struct {
	stats map[string]*AnalysisStats
	mu    sync.RWMutex
}

// AnalysisStats 分析统计信息
type AnalysisStats struct {
	TaskID       string
	Strategy     string
	TotalCount   int64
	SuccessCount int64
	ErrorCount   int64
	TotalTime    time.Duration
	AvgTime      time.Duration
	LastAnalysis time.Time
}

// NewAnalysisStatsCollector 创建统计收集器
func NewAnalysisStatsCollector() *AnalysisStatsCollector {
	return &AnalysisStatsCollector{
		stats: make(map[string]*AnalysisStats),
	}
}

// RecordAnalysis 记录分析统计
func (asc *AnalysisStatsCollector) RecordAnalysis(taskID, strategy string, duration time.Duration, success bool) {
	asc.mu.Lock()
	defer asc.mu.Unlock()

	key := fmt.Sprintf("%s:%s", taskID, strategy)
	stat, exists := asc.stats[key]
	if !exists {
		stat = &AnalysisStats{
			TaskID:   taskID,
			Strategy: strategy,
		}
		asc.stats[key] = stat
	}

	stat.TotalCount++
	stat.TotalTime += duration
	stat.AvgTime = stat.TotalTime / time.Duration(stat.TotalCount)
	stat.LastAnalysis = time.Now()

	if success {
		stat.SuccessCount++
	} else {
		stat.ErrorCount++
	}
}

// IntelligentAnalyzer 智能分析器：规则引擎 + 表达式引擎 + ML分析 + 阈值分析
type IntelligentAnalyzer struct {
	mlAnalyzer        *AnomalyDetector   // 异常检测器
	thresholdAnalyzer *ThresholdAnalyzer // 阈值分析器

	// 存储和配置
	repos  *repository.Repositories
	logger *zap.Logger
	config *IntelligentConfig

	// 规则管理
	ruleManager *RuleManager
	exprManager *ExpressionManager
	// 缓存和统计
	resultCache    *ResultCache
	statsCollector *AnalysisStatsCollector
}

// IntelligentConfig 智能分析器配置
type IntelligentConfig struct {
	Strategy   AnalysisStrategy `json:"strategy"`
	Rules      RulesConfig      `json:"rules"`
	Expression ExpressionConfig `json:"expression"`
	ML         MLConfig         `json:"ml"`
	Threshold  ThresholdConfig  `json:"threshold"`
	Cache      CacheConfig      `json:"cache"`
	Monitoring MonitoringConfig `json:"monitoring"`
}

// AnalysisStrategy 分析策略
type AnalysisStrategy struct {
	Mode             string        `json:"mode"` // "rules_first", "expr_first", "ml_first", "threshold_first", "cascade", "parallel"
	EnableRules      bool          `json:"enable_rules"`
	EnableExpression bool          `json:"enable_expression"`
	EnableML         bool          `json:"enable_ml"`
	EnableThreshold  bool          `json:"enable_threshold"`
	Timeout          time.Duration `json:"timeout"`
	FallbackStrategy string        `json:"fallback_strategy"` // 当主策略失败时的后备策略
}

// RulesConfig 规则引擎配置
type RulesConfig struct {
	OptimizationLevel int           `json:"optimization_level"`
	Concurrent        bool          `json:"concurrent"`
	MaxWorkers        int           `json:"max_workers"`
	EnableStats       bool          `json:"enable_stats"`
	RuleTimeout       time.Duration `json:"rule_timeout"`
	RuleSources       []RuleSource  `json:"rule_sources"`
}

// RuleSource 规则源配置
type RuleSource struct {
	Type     string            `json:"type"` // "database", "file", "api"
	Config   map[string]string `json:"config"`
	Priority int               `json:"priority"`
	Enabled  bool              `json:"enabled"`
}

// ExpressionConfig 表达式引擎配置
type ExpressionConfig struct {
	DatabaseEnabled bool          `json:"database_enabled"`
	FileEnabled     bool          `json:"file_enabled"`
	ExpressionsPath string        `json:"expressions_path"`
	ReloadCycle     time.Duration `json:"reload_cycle"`
}

// MLConfig ML分析配置
type MLConfig struct {
	DefaultAlgorithm string                 `json:"default_algorithm"`
	Algorithms       map[string]interface{} `json:"algorithms"`
	ModelPath        string                 `json:"model_path"`
	EnableFallback   bool                   `json:"enable_fallback"`
}

// ThresholdConfig 阈值分析配置
type ThresholdConfig struct {
	Enabled   bool          `json:"enabled"`
	Threshold float64       `json:"threshold"`
	Timeout   time.Duration `json:"timeout"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Size int           `json:"size"`
	TTL  time.Duration `json:"ttl"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	EnableMetrics bool `json:"enable_metrics"`
	MetricsPort   int  `json:"metrics_port"`
}

// NewIntelligentAnalyzer 创建智能分析器
func NewIntelligentAnalyzer(repos *repository.Repositories, logger *zap.Logger, config *IntelligentConfig) (*IntelligentAnalyzer, error) {
	if config == nil {
		config = DefaultIntelligentConfig()
	}

	// 创建ML分析器
	mlAnalyzer := NewAnomalyDetector(repos, logger)

	// 创建阈值分析器
	thresholdAnalyzer := NewThresholdAnalyzer(repos, logger)

	// 创建管理器
	ruleConfig := RuleManagerConfig{
		ReloadInterval:       5 * time.Minute,
		EnableOptimization:   true,
		MaxConcurrentRules:   config.Rules.MaxWorkers,
		RuleExecutionTimeout: 30 * time.Second,
	}

	ruleManager, err := NewRuleManager(ruleConfig)
	if err != nil {
		return nil, fmt.Errorf("创建规则管理器失败: %w", err)
	}

	// 创建表达式管理器
	exprManager, err := NewExpressionManager(repos.Expression, logger, config.Expression)
	if err != nil {
		return nil, fmt.Errorf("创建表达式管理器失败: %w", err)
	}

	// 创建缓存和统计
	resultCache := NewResultCache(config.Cache.Size, config.Cache.TTL)
	statsCollector := NewAnalysisStatsCollector()

	analyzer := &IntelligentAnalyzer{
		mlAnalyzer:        mlAnalyzer,
		thresholdAnalyzer: thresholdAnalyzer,
		repos:             repos,
		logger:            logger,
		config:            config,
		ruleManager:       ruleManager,
		exprManager:       exprManager,
		resultCache:       resultCache,
		statsCollector:    statsCollector,
	}

	// 初始化
	if err := analyzer.initialize(); err != nil {
		return nil, fmt.Errorf("初始化智能分析器失败: %w", err)
	}

	return analyzer, nil
}

// initialize 初始化分析器
func (ia *IntelligentAnalyzer) initialize() error {
	// 注册自定义函数
	if err := ia.registerCustomFunctions(); err != nil {
		return fmt.Errorf("注册自定义函数失败: %w", err)
	}

	// 启用统计收集
	if ia.config.Rules.EnableStats {
		rules.EnableStatisticsCollection()
	}

	ia.logger.Info("智能分析器初始化完成",
		zap.String("strategy", ia.config.Strategy.Mode))

	return nil
}

// registerCustomFunctions 注册自定义函数
func (ia *IntelligentAnalyzer) registerCustomFunctions() error {
	// TODO: 实现自定义函数注册
	// 例如：trend analysis, pattern detection, health evaluation 等
	return nil
}

// prepareAnalysisData 准备分析数据
func (ia *IntelligentAnalyzer) prepareAnalysisData(ctx context.Context, analysisCtx *AnalysisContext) (*AnalysisData, error) {
	// 从数据库获取相关数据
	data := &AnalysisData{
		TaskID:    analysisCtx.TaskID,
		TaskType:  "analysis",
		Category:  "intelligent",
		Variables: make(map[string]interface{}),
		Metrics:   make([]*model.MetricData, 0),
		Context:   analysisCtx.Config,
	}

	// 获取最新的指标数据
	if metrics, err := ia.repos.Metric.GetLatestByMetricName("cpu_usage"); err == nil && metrics != nil {
		data.Metrics = append(data.Metrics, metrics)
		data.Variables["cpu_usage"] = metrics.Value
	}

	if metrics, err := ia.repos.Metric.GetLatestByMetricName("memory_usage"); err == nil && metrics != nil {
		data.Metrics = append(data.Metrics, metrics)
		data.Variables["memory_usage"] = metrics.Value
	}

	if metrics, err := ia.repos.Metric.GetLatestByMetricName("memory_usage_percent"); err == nil && metrics != nil {
		data.Metrics = append(data.Metrics, metrics)
		data.Variables["memory_usage_percent"] = metrics.Value
	}

	if metrics, err := ia.repos.Metric.GetLatestByMetricName("disk_usage"); err == nil && metrics != nil {
		data.Metrics = append(data.Metrics, metrics)
		data.Variables["disk_usage"] = metrics.Value
	}

	if metrics, err := ia.repos.Metric.GetLatestByMetricName("disk_usage_percent"); err == nil && metrics != nil {
		data.Metrics = append(data.Metrics, metrics)
		data.Variables["disk_usage_percent"] = metrics.Value
	}

	return data, nil
}

// executeRules 执行规则引擎
func (ia *IntelligentAnalyzer) executeRules(ctx context.Context, analysisCtx *AnalysisContext, data *AnalysisData) (*AnalysisResult, error) {
	// 转换数据格式
	ruleData := make(map[string]interface{})
	maps.Copy(ruleData, data.Variables)

	// 执行规则
	results, err := ia.ruleManager.ExecuteRules(ctx, ruleData)
	if err != nil {
		return nil, fmt.Errorf("规则执行失败: %w", err)
	}

	// 构建分析结果
	result := &AnalysisResult{
		TaskID:    data.TaskID,
		Type:      "rule_analysis",
		Algorithm: "rule_engine",
		Status:    "normal",
		Message:   "规则分析完成",
		Severity:  "info",
		Details:   make(map[string]interface{}),
		Timestamp: time.Now(),
	}

	// 处理规则结果
	if len(results) > 0 {
		matchedCount := 0
		totalScore := 0.0

		for _, ruleResult := range results {
			if ruleResult.Matched {
				matchedCount++
				totalScore += ruleResult.Score
			}
		}

		if matchedCount > 0 {
			result.Status = "anomaly_detected"
			result.Message = fmt.Sprintf("检测到 %d 个规则匹配", matchedCount)
			result.Severity = "warning"
			if totalScore/float64(matchedCount) > 0.8 {
				result.Severity = "critical"
			}
		}

		result.Details["rule_results"] = results
		result.Details["matched_rules"] = matchedCount
		result.Details["total_rules"] = len(results)
		result.Details["confidence"] = float64(matchedCount) / float64(len(results))
		result.Details["average_score"] = totalScore / float64(len(results))
	}

	return result, nil
}

// executeExpressions 执行表达式引擎
func (ia *IntelligentAnalyzer) executeExpressions(ctx context.Context, analysisCtx *AnalysisContext, data *AnalysisData) (*AnalysisResult, error) {
	// 转换数据格式
	exprData := make(map[string]any)
	for k, v := range data.Variables {
		exprData[k] = v
	}

	// 执行表达式
	results, err := ia.exprManager.ExecuteExpressions(ctx, exprData)
	if err != nil {
		return nil, fmt.Errorf("表达式执行失败: %w", err)
	}

	// 构建分析结果
	result := &AnalysisResult{
		TaskID:    data.TaskID,
		Type:      "expression_analysis",
		Algorithm: "expression_engine",
		Status:    "normal",
		Message:   "表达式分析完成",
		Severity:  "info",
		Details:   make(map[string]interface{}),
		Timestamp: time.Now(),
	}

	// 处理表达式结果
	if len(results) > 0 {
		successCount := 0
		totalValue := 0.0

		for _, exprResult := range results {
			if exprResult.Success {
				successCount++
				if val, ok := exprResult.Value.(float64); ok {
					totalValue += val
				}
			}
		}

		if successCount > 0 {
			avgValue := totalValue / float64(successCount)
			if avgValue > 0.5 { // 阈值可配置
				result.Status = "anomaly_detected"
				result.Message = fmt.Sprintf("表达式检测到异常，平均值: %.2f", avgValue)
				result.Severity = "warning"
			} else {
				result.Status = "normal"
				result.Message = fmt.Sprintf("表达式分析正常，平均值: %.2f", avgValue)
			}
		}

		result.Details["expression_results"] = results
		result.Details["successful_expressions"] = successCount
		result.Details["total_expressions"] = len(results)
		result.Details["confidence"] = float64(successCount) / float64(len(results))
		result.Details["average_value"] = totalValue / float64(len(results))
	}

	return result, nil
}

// executeMLAnalysis 执行ML分析
func (ia *IntelligentAnalyzer) executeMLAnalysis(ctx context.Context, analysisCtx *AnalysisContext, data *AnalysisData) (*AnalysisResult, error) {
	if ia.mlAnalyzer == nil {
		return nil, fmt.Errorf("ML分析器未初始化")
	}

	// 准备异常检测数据
	mlData := make(map[string]interface{})
	maps.Copy(mlData, data.Variables)
	maps.Copy(mlData, analysisCtx.Config)

	// 设置默认参数（如果配置中没有指定）
	if _, exists := mlData["algorithm"]; !exists {
		mlData["algorithm"] = ia.config.ML.DefaultAlgorithm
	}
	if _, exists := mlData["window"]; !exists {
		mlData["window"] = "1h"
	}

	// 确保有指标名称
	if _, exists := mlData["metric"]; !exists {
		// 尝试从可用指标中选择第一个
		if len(data.Metrics) > 0 {
			mlData["metric"] = data.Metrics[0].MetricName
		} else {
			// 使用默认指标
			mlData["metric"] = "cpu_usage"
		}
	}

	// 执行异常检测
	result, err := ia.mlAnalyzer.Analyze(ctx, analysisCtx.TaskID, mlData)
	if err != nil {
		return nil, fmt.Errorf("异常检测分析失败: %w", err)
	}

	// 确保结果有正确的类型标识
	if result != nil {
		result.Type = "ml_analysis"
		if result.Algorithm == "" {
			result.Algorithm = "anomaly_detection"
		}
	}

	return result, nil
}

// executeThresholdAnalysis 执行阈值分析
func (ia *IntelligentAnalyzer) executeThresholdAnalysis(ctx context.Context, analysisCtx *AnalysisContext, data *AnalysisData) (*AnalysisResult, error) {
	if ia.thresholdAnalyzer == nil {
		return nil, fmt.Errorf("阈值分析器未初始化")
	}

	// 准备阈值分析数据
	thresholdData := make(map[string]interface{})
	maps.Copy(thresholdData, data.Variables)
	maps.Copy(thresholdData, analysisCtx.Config)

	// 执行阈值分析
	result, err := ia.thresholdAnalyzer.Analyze(ctx, analysisCtx.TaskID, thresholdData)
	if err != nil {
		return nil, fmt.Errorf("阈值分析失败: %w", err)
	}

	// 设置算法标识
	if result != nil {
		result.Algorithm = "threshold_analysis"
	}

	return result, nil
}

// Analyze 执行智能分析
func (ia *IntelligentAnalyzer) Analyze(ctx context.Context, taskID string, config map[string]interface{}) (*AnalysisResult, error) {
	startTime := time.Now()

	// 创建分析上下文
	analysisCtx := &AnalysisContext{
		TaskID:    taskID,
		Config:    config,
		StartTime: startTime,
		Logger:    ia.logger.With(zap.String("task_id", taskID)),
	}

	// 检查缓存
	if result := ia.resultCache.Get(taskID, config); result != nil {
		analysisCtx.Logger.Debug("使用缓存结果")
		return result, nil
	}

	// 准备分析数据
	data, err := ia.prepareAnalysisData(ctx, analysisCtx)
	if err != nil {
		return nil, fmt.Errorf("准备分析数据失败: %w", err)
	}

	// 根据策略执行分析
	var result *AnalysisResult
	switch ia.config.Strategy.Mode {
	case "rules_first":
		result, err = ia.executeRulesFirstStrategy(ctx, analysisCtx, data)
	case "expr_first":
		result, err = ia.executeExprFirstStrategy(ctx, analysisCtx, data)
	case "ml_first":
		result, err = ia.executeMLFirstStrategy(ctx, analysisCtx, data)
	case "threshold_first":
		result, err = ia.executeThresholdFirstStrategy(ctx, analysisCtx, data)
	case "cascade":
		result, err = ia.executeCascadeStrategy(ctx, analysisCtx, data)
	case "parallel":
		result, err = ia.executeParallelStrategy(ctx, analysisCtx, data)
	default:
		result, err = ia.executeRulesFirstStrategy(ctx, analysisCtx, data)
	}

	if err != nil {
		// 尝试后备策略
		if ia.config.Strategy.FallbackStrategy != "" {
			analysisCtx.Logger.Warn("主策略失败，尝试后备策略", zap.Error(err))
			result, err = ia.executeFallbackStrategy(ctx, analysisCtx, data)
		}
	}

	if err != nil {
		return nil, err
	}

	// 记录统计信息
	duration := time.Since(startTime)
	ia.statsCollector.RecordAnalysis(analysisCtx.TaskID, ia.config.Strategy.Mode, duration, err == nil)

	// 缓存结果
	if result != nil {
		ia.resultCache.Set(taskID, config, result)
	}

	analysisCtx.Logger.Info("分析完成",
		zap.Duration("duration", duration),
		zap.String("strategy", ia.config.Strategy.Mode),
		zap.String("status", result.Status))

	return result, nil
}

// DefaultIntelligentConfig 默认配置
func DefaultIntelligentConfig() *IntelligentConfig {
	return &IntelligentConfig{
		Strategy: AnalysisStrategy{
			Mode:             "rules_first",
			EnableRules:      true,
			EnableExpression: true,
			EnableML:         true,
			EnableThreshold:  true,
			Timeout:          30 * time.Second,
			FallbackStrategy: "ml_first",
		},
		Rules: RulesConfig{
			OptimizationLevel: 3, // Advanced optimization
			Concurrent:        true,
			MaxWorkers:        4,
			EnableStats:       true,
			RuleTimeout:       5 * time.Second,
			RuleSources: []RuleSource{
				{
					Type:     "database",
					Config:   map[string]string{"table": "analysis_rules"},
					Priority: 1,
					Enabled:  true,
				},
			},
		},
		Expression: ExpressionConfig{
			DatabaseEnabled: true,
			FileEnabled:     false,
			ExpressionsPath: "/etc/aiops/expressions",
			ReloadCycle:     5 * time.Minute,
		},
		ML: MLConfig{
			DefaultAlgorithm: "statistical",
			Algorithms: map[string]interface{}{
				"statistical": map[string]interface{}{"threshold": 2.0},
				"iqr":         map[string]interface{}{"iqr_multiplier": 1.5},
			},
			EnableFallback: true,
		},
		Threshold: ThresholdConfig{
			Enabled:   true,
			Threshold: 0.5,
			Timeout:   10 * time.Second,
		},
		Cache: CacheConfig{
			Size: 1000,
			TTL:  5 * time.Minute,
		},
		Monitoring: MonitoringConfig{
			EnableMetrics: true,
			MetricsPort:   8080,
		},
	}
}
