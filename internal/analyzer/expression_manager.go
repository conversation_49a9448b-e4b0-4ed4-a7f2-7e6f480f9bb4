package analyzer

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops/internal/repository"
	"aiops/pkg/expr"
	"aiops/pkg/expr/ast"

	"maps"

	"go.uber.org/zap"
)

// ExpressionManager 管理表达式的编译、缓存和执行
type ExpressionManager struct {
	engine      expr.ExprEngine
	exprRepo    repository.ExpressionRepository
	cache       map[string]*CompiledExpression
	cacheMutex  sync.RWMutex
	lastLoad    time.Time
	reloadCycle time.Duration
	logger      *zap.Logger
}

// CompiledExpression 编译后的表达式
type CompiledExpression struct {
	ID          string          `json:"id"`
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Expression  string          `json:"expression"`
	Program     *ast.Expression `json:"-"`
	ReturnType  string          `json:"return_type"`
	Variables   map[string]any  `json:"variables"`
	Priority    int             `json:"priority"`
	Enabled     bool            `json:"enabled"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

// ExpressionResult 表达式执行结果
type ExpressionResult struct {
	ExpressionID   string         `json:"expression_id"`
	ExpressionName string         `json:"expression_name"`
	Value          any            `json:"value"`
	Success        bool           `json:"success"`
	ExecutedAt     time.Time      `json:"executed_at"`
	ExecuteTime    time.Duration  `json:"execute_time"`
	Context        map[string]any `json:"context"`
	Error          error          `json:"error,omitempty"`
}

// NewExpressionManager 创建新的表达式管理器
func NewExpressionManager(exprRepo repository.ExpressionRepository, logger *zap.Logger, config ExpressionConfig) (*ExpressionManager, error) {
	engine := expr.NewExprEngine()

	em := &ExpressionManager{
		engine:      engine,
		exprRepo:    exprRepo,
		cache:       make(map[string]*CompiledExpression),
		reloadCycle: config.ReloadCycle,
		logger:      logger,
	}

	// 如果重载周期为0，设置默认值
	if em.reloadCycle == 0 {
		em.reloadCycle = 5 * time.Minute
	}

	// 初始化加载表达式
	if err := em.loadExpressions(); err != nil {
		return nil, fmt.Errorf("failed to load initial expressions: %w", err)
	}

	// 启动定期重载
	go em.startReloadLoop()

	return em, nil
}

// loadExpressions 从数据库和文件加载表达式
func (em *ExpressionManager) loadExpressions() error {
	em.cacheMutex.Lock()
	defer em.cacheMutex.Unlock()

	// 清空缓存
	em.cache = make(map[string]*CompiledExpression)

	// 从数据库加载表达式
	if em.exprRepo != nil {
		if err := em.loadExpressionsFromDB(); err != nil {
			em.logger.Error("Failed to load expressions from database", zap.Error(err))
		}
	}

	em.lastLoad = time.Now()
	em.logger.Info("Expressions reloaded", zap.Int("count", len(em.cache)))

	return nil
}

// loadExpressionsFromDB 从数据库加载表达式
func (em *ExpressionManager) loadExpressionsFromDB() error {
	// 获取启用的表达式
	expressions, err := em.exprRepo.GetEnabled()
	if err != nil {
		return fmt.Errorf("failed to get enabled expressions: %w", err)
	}

	for _, expr := range expressions {
		// 解析变量
		variables, err := expr.GetVariables()
		if err != nil {
			em.logger.Error("Failed to parse expression variables",
				zap.String("expression_id", expr.ID),
				zap.Error(err))
			variables = make([]string, 0)
		}

		// 转换变量为 map
		variablesMap := make(map[string]any)
		for _, v := range variables {
			variablesMap[v] = nil // 默认值为 nil
		}

		// 编译表达式
		program, err := em.engine.Compile(expr.Expression)
		if err != nil {
			em.logger.Error("Failed to compile expression",
				zap.String("expression_id", expr.ID),
				zap.Error(err))
			continue
		}

		compiledExpr := &CompiledExpression{
			ID:          expr.ID,
			Name:        expr.Name,
			Description: expr.Description,
			Expression:  expr.Expression,
			Program:     &program,
			ReturnType:  string(expr.ReturnType),
			Variables:   variablesMap,
			Priority:    0, // 可以从配置中获取
			Enabled:     expr.IsEnabled(),
			CreatedAt:   expr.CreatedAt,
			UpdatedAt:   expr.UpdatedAt,
		}

		em.cache[expr.ID] = compiledExpr
	}

	return nil
}

// ExecuteExpressions 执行所有适用的表达式
func (em *ExpressionManager) ExecuteExpressions(ctx context.Context, data map[string]any) ([]ExpressionResult, error) {
	em.cacheMutex.RLock()
	defer em.cacheMutex.RUnlock()

	var results []ExpressionResult

	for _, expr := range em.cache {
		if !expr.Enabled {
			continue
		}

		startTime := time.Now()

		// 准备环境变量
		env := make(map[string]any)
		for k, v := range data {
			env[k] = v
		}
		for k, v := range expr.Variables {
			env[k] = v
		}

		// 执行表达式
		value, err := em.engine.Evaluate(expr.Expression, env)

		executeTime := time.Since(startTime)

		result := ExpressionResult{
			ExpressionID:   expr.ID,
			ExpressionName: expr.Name,
			Value:          value,
			Success:        err == nil,
			ExecutedAt:     startTime,
			ExecuteTime:    executeTime,
			Context:        data,
			Error:          err,
		}

		results = append(results, result)

		if err != nil {
			em.logger.Error("Expression execution failed",
				zap.String("expression_id", expr.ID),
				zap.String("expression_name", expr.Name),
				zap.Error(err))
		}
	}

	return results, nil
}

// ExecuteExpression 执行单个表达式
func (em *ExpressionManager) ExecuteExpression(ctx context.Context, id string, data map[string]any) (*ExpressionResult, error) {
	em.cacheMutex.RLock()
	expr, exists := em.cache[id]
	em.cacheMutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("expression not found: %s", id)
	}

	if !expr.Enabled {
		return &ExpressionResult{
			ExpressionID:   expr.ID,
			ExpressionName: expr.Name,
			Success:        false,
			ExecutedAt:     time.Now(),
			Error:          fmt.Errorf("expression is disabled"),
		}, nil
	}

	startTime := time.Now()

	// 准备环境变量
	env := make(map[string]any)
	maps.Copy(env, data)
	maps.Copy(env, expr.Variables)

	// 执行表达式
	value, err := em.engine.Evaluate(expr.Expression, env)
	executeTime := time.Since(startTime)

	result := &ExpressionResult{
		ExpressionID:   expr.ID,
		ExpressionName: expr.Name,
		Value:          value,
		Success:        err == nil,
		ExecutedAt:     startTime,
		ExecuteTime:    executeTime,
		Context:        data,
		Error:          err,
	}

	return result, nil
}

// GetExpression 获取指定ID的表达式
func (em *ExpressionManager) GetExpression(id string) (*CompiledExpression, bool) {
	em.cacheMutex.RLock()
	defer em.cacheMutex.RUnlock()

	expr, exists := em.cache[id]
	return expr, exists
}

// GetAllExpressions 获取所有表达式
func (em *ExpressionManager) GetAllExpressions() map[string]*CompiledExpression {
	em.cacheMutex.RLock()
	defer em.cacheMutex.RUnlock()

	result := make(map[string]*CompiledExpression)
	maps.Copy(result, em.cache)
	return result
}

// GetExpressionCount 获取表达式数量
func (em *ExpressionManager) GetExpressionCount() int {
	em.cacheMutex.RLock()
	defer em.cacheMutex.RUnlock()
	return len(em.cache)
}

// CompileAndCache 编译并缓存新表达式
func (em *ExpressionManager) CompileAndCache(id, name, expression string, variables map[string]any) error {
	program, err := em.engine.Compile(expression)
	if err != nil {
		return fmt.Errorf("failed to compile expression: %w", err)
	}

	em.cacheMutex.Lock()
	defer em.cacheMutex.Unlock()

	em.cache[id] = &CompiledExpression{
		ID:         id,
		Name:       name,
		Expression: expression,
		Program:    &program,
		Variables:  variables,
		Enabled:    true,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	return nil
}

// RemoveFromCache 从缓存中移除表达式
func (em *ExpressionManager) RemoveFromCache(id string) {
	em.cacheMutex.Lock()
	defer em.cacheMutex.Unlock()
	delete(em.cache, id)
}

// startReloadLoop 启动表达式重载循环
func (em *ExpressionManager) startReloadLoop() {
	ticker := time.NewTicker(em.reloadCycle)
	defer ticker.Stop()

	for range ticker.C {
		if err := em.loadExpressions(); err != nil {
			em.logger.Error("Failed to reload expressions", zap.Error(err))
		}
	}
}

// Close 关闭表达式管理器
func (em *ExpressionManager) Close() error {
	// 停止重载循环等清理工作
	return nil
}
