package api

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"aiops/internal/config"
	"aiops/internal/controller"
	"aiops/internal/repository"
	"aiops/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Server HTTP API 服务器
type Server struct {
	config      config.ServerConfig
	controllers *controller.Controllers
	logger      *zap.Logger
	server      *http.Server
	router      *gin.Engine
}

// NewServer 创建 API 服务器实例
func NewServer(cfg config.ServerConfig, db *gorm.DB, scheduler controller.Scheduler, logger *zap.Logger) *Server {
	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())
	router.Use(loggingMiddleware(logger))

	// 初始化仓库层
	repos := repository.NewRepositories(db, logger)

	// 初始化服务层
	services := service.NewServices(repos, logger)

	// 初始化控制器层
	controllers := controller.NewControllers(services, scheduler, logger)

	server := &Server{
		config:      cfg,
		controllers: controllers,
		logger:      logger,
		router:      router,
	}

	// 设置路由
	server.setupRoutes()

	return server
}

// Start 启动服务器
func (s *Server) Start() error {
	s.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", s.config.Port),
		Handler: s.router,
	}

	go func() {
		if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP 服务器启动失败", zap.Error(err))
		}
	}()

	s.logger.Info("HTTP API 服务器启动成功", zap.Int("port", s.config.Port))
	return nil
}

// Stop 停止服务器
func (s *Server) Stop() error {
	if s.server == nil {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return s.server.Shutdown(ctx)
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	api := s.router.Group("/api/v1")

	// 任务管理 API
	tasks := api.Group("/tasks")
	{
		tasks.POST("", s.controllers.Task.CreateTask)
		tasks.GET("", s.controllers.Task.GetTasks)
		tasks.GET("/:id", s.controllers.Task.GetTask)
		tasks.PUT("/:id", s.controllers.Task.UpdateTask)
		tasks.DELETE("/:id", s.controllers.Task.DeleteTask)
		tasks.POST("/:id/start", s.controllers.Task.StartTask)
		tasks.POST("/:id/stop", s.controllers.Task.StopTask)
		tasks.GET("/:id/executions", s.controllers.Task.GetTaskExecutions)
	}

	// 监控数据 API
	metrics := api.Group("/metrics")
	{
		metrics.GET("", s.controllers.Metric.GetMetrics)
		metrics.GET("/:task_id/:metric_name", s.controllers.Metric.GetMetricData)
	}

	// 日志 API
	logs := api.Group("/logs")
	{
		logs.GET("", s.controllers.Log.GetLogs)
		logs.GET("/:task_id", s.controllers.Log.GetTaskLogs)
	}

	// 告警 API
	alerts := api.Group("/alerts")
	{
		alerts.GET("", s.controllers.Alert.GetAlerts)
		alerts.GET("/unresolved", s.controllers.Alert.GetUnresolvedAlerts)
		alerts.POST("/:id/resolve", s.controllers.Alert.ResolveAlert)
	}

	// 规则管理 API
	rules := api.Group("/rules")
	{
		rules.POST("", s.controllers.Rule.CreateRule)
		rules.GET("", s.controllers.Rule.GetRules)
		rules.GET("/:id", s.controllers.Rule.GetRule)
		rules.PUT("/:id", s.controllers.Rule.UpdateRule)
		rules.DELETE("/:id", s.controllers.Rule.DeleteRule)
		rules.POST("/:id/enable", s.controllers.Rule.EnableRule)
		rules.POST("/:id/disable", s.controllers.Rule.DisableRule)
		rules.GET("/categories", s.controllers.Rule.GetRuleCategories)
		rules.GET("/action-types", s.controllers.Rule.GetActionTypes)
	}

	// 健康检查
	s.router.GET("/health", s.controllers.Health.HealthCheck)

	// 静态文件服务 (如果有 Web UI)
	s.router.Static("/static", "./web/static")
	s.router.StaticFile("/", "./web/index.html")
}

// corsMiddleware CORS 中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// loggingMiddleware 日志中间件
func loggingMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		c.Next()

		// 跳过健康检查等定时请求的日志，减少噪音
		if path == "/health" || path == "/api/v1/health" {
			return
		}

		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		logger.Info("HTTP Request",
			zap.String("method", method),
			zap.String("path", path),
			zap.Int("status", statusCode),
			zap.String("ip", clientIP),
			zap.Duration("latency", latency),
		)
	}
}
