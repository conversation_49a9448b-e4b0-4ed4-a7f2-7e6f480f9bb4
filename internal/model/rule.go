package model

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// RuleCategory 规则类别
type RuleCategory string

const (
	RuleCategoryThreshold   RuleCategory = "threshold"   // 阈值类规则
	RuleCategoryAnomaly     RuleCategory = "anomaly"     // 异常检测类规则
	RuleCategoryPerformance RuleCategory = "performance" // 性能类规则
	RuleCategoryError       RuleCategory = "error"       // 错误类规则
	RuleCategoryBusiness    RuleCategory = "business"    // 业务类规则
	RuleCategorySecurity    RuleCategory = "security"    // 安全类规则
	RuleCategorySystem      RuleCategory = "system"      // 系统类规则
	RuleCategoryCustom      RuleCategory = "custom"      // 自定义规则
)

// RuleStatus 规则状态
type RuleStatus string

const (
	RuleStatusEnabled  RuleStatus = "enabled"  // 启用
	RuleStatusDisabled RuleStatus = "disabled" // 禁用
	RuleStatusDraft    RuleStatus = "draft"    // 草稿
)

// ActionDefinition 动作定义
type ActionDefinition struct {
	Type   string         `json:"type"` // "alert", "scale", "restart", "log", "modify"
	Config map[string]any `json:"config"`
}

// Rule 规则模型
type Rule struct {
	gorm.Model
	ID          string          `json:"id" gorm:"uniqueIndex"`
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Category    RuleCategory    `json:"category"`
	TaskTypes   json.RawMessage `json:"task_types" gorm:"type:json"` // 适用的任务类型
	TaskType    string          `json:"task_type"`                   // 单个任务类型 (向后兼容)
	Priority    int             `json:"priority"`
	Status      RuleStatus      `json:"status"`
	Enabled     bool            `json:"enabled"` // 是否启用

	// 规则定义
	ConditionDSL json.RawMessage `json:"condition_dsl" gorm:"type:json"` // DSL条件
	Condition    string          `json:"condition"`                      // 简单条件表达式 (向后兼容)
	Actions      json.RawMessage `json:"actions" gorm:"type:json"`       // 动作定义
	ActionType   ActionType      `json:"action_type"`                    // 单个动作类型 (向后兼容)
	ActionConfig json.RawMessage `json:"action_config" gorm:"type:json"` // 单个动作配置 (向后兼容)

	// 元数据
	Tags    json.RawMessage `json:"tags" gorm:"type:json"` // 标签
	Version string          `json:"version"`
	Author  string          `json:"author"`

	// 运行时配置
	Config json.RawMessage `json:"config" gorm:"type:json"`

	// 统计信息
	MatchCount int        `json:"match_count"` // 匹配次数
	LastMatch  *time.Time `json:"last_match"`  // 最后匹配时间
}

// TableName 指定表名
func (Rule) TableName() string {
	return "rules"
}

// GetTaskTypes 获取适用的任务类型
func (r *Rule) GetTaskTypes() ([]string, error) {
	var taskTypes []string
	if len(r.TaskTypes) > 0 {
		if err := json.Unmarshal(r.TaskTypes, &taskTypes); err != nil {
			return nil, err
		}
	}
	return taskTypes, nil
}

// SetTaskTypes 设置适用的任务类型
func (r *Rule) SetTaskTypes(taskTypes []string) error {
	data, err := json.Marshal(taskTypes)
	if err != nil {
		return err
	}
	r.TaskTypes = data
	return nil
}

// GetActions 获取动作定义
func (r *Rule) GetActions() ([]ActionDefinition, error) {
	var actions []ActionDefinition
	if len(r.Actions) > 0 {
		if err := json.Unmarshal(r.Actions, &actions); err != nil {
			return nil, err
		}
	}
	return actions, nil
}

// SetActions 设置动作定义
func (r *Rule) SetActions(actions []ActionDefinition) error {
	data, err := json.Marshal(actions)
	if err != nil {
		return err
	}
	r.Actions = data
	return nil
}

// GetTags 获取标签
func (r *Rule) GetTags() ([]string, error) {
	var tags []string
	if len(r.Tags) > 0 {
		if err := json.Unmarshal(r.Tags, &tags); err != nil {
			return nil, err
		}
	}
	return tags, nil
}

// SetTags 设置标签
func (r *Rule) SetTags(tags []string) error {
	data, err := json.Marshal(tags)
	if err != nil {
		return err
	}
	r.Tags = data
	return nil
}

// GetConfig 获取配置
func (r *Rule) GetConfig() (map[string]any, error) {
	var config map[string]any
	if len(r.Config) > 0 {
		if err := json.Unmarshal(r.Config, &config); err != nil {
			return nil, err
		}
	}
	return config, nil
}

// SetConfig 设置配置
func (r *Rule) SetConfig(config map[string]any) error {
	data, err := json.Marshal(config)
	if err != nil {
		return err
	}
	r.Config = data
	return nil
}

// GetConditionDSL 获取条件DSL
func (r *Rule) GetConditionDSL() (map[string]any, error) {
	var condition map[string]any
	if len(r.ConditionDSL) > 0 {
		if err := json.Unmarshal(r.ConditionDSL, &condition); err != nil {
			return nil, err
		}
	}
	return condition, nil
}

// SetConditionDSL 设置条件DSL
func (r *Rule) SetConditionDSL(condition map[string]any) error {
	data, err := json.Marshal(condition)
	if err != nil {
		return err
	}
	r.ConditionDSL = data
	return nil
}

// IsEnabled 检查规则是否启用
func (r *Rule) IsEnabled() bool {
	return r.Status == RuleStatusEnabled
}

// ActionType 动作类型
type ActionType string

const (
	ActionTypeAlert   ActionType = "alert"   // 告警动作
	ActionTypeEmail   ActionType = "email"   // 邮件动作
	ActionTypeWebhook ActionType = "webhook" // Webhook动作
	ActionTypeLog     ActionType = "log"     // 日志动作
	ActionTypeScript  ActionType = "script"  // 脚本动作
)

// ErrInvalidInput 创建无效输入错误
func ErrInvalidInput(message string) error {
	return fmt.Errorf("invalid input: %s", message)
}
