# AIOps 智能分析示例文档

## 概述

本目录包含了完整的智能分析示例，展示了AIOps平台的强大分析能力。这些示例结合了规则引擎、表达式引擎、机器学习和传统阈值分析，提供了全面的智能运维解决方案。

## 分析示例列表

### 1. 综合智能分析 (comprehensive_intelligent_analysis.json)

**目的**: 展示AIOps平台的完整智能分析能力

**特性**:
- **级联策略分析**: 按优先级依次执行不同分析方法
- **规则引擎**: 基于预定义规则进行快速决策
- **表达式引擎**: 灵活的表达式计算和条件判断
- **机器学习**: 异常检测和模式识别
- **阈值分析**: 传统阈值监控
- **智能缓存**: 提高分析效率
- **性能监控**: 分析过程的性能指标收集

**适用场景**:
- 生产环境综合监控
- 智能运维决策支持
- 异常检测和预警
- 性能优化建议
- 容量规划辅助

### 2. 系统性能智能分析 (system_performance_intelligent_analysis.json)

**目的**: 专门针对系统性能的智能分析

**分析目标**:
- **高CPU使用率检测**: 使用表达式检测CPU异常和持续高负载
- **内存泄漏检测**: 通过趋势分析识别内存泄漏模式
- **磁盘空间预警**: 传统阈值检测结合预测分析
- **系统健康评分**: 综合多个指标计算系统健康度
- **性能异常检测**: ML算法识别性能异常模式
- **资源趋势分析**: 预测资源使用趋势

**关键特性**:
- 规则优先策略，快速响应已知问题
- 表达式引擎支持复杂的条件判断
- 统计学方法和隔离森林算法结合
- 自动告警和建议操作

### 3. 数据库智能分析 (database_intelligent_analysis.json)

**目的**: MySQL数据库性能和健康状况的智能分析

**分析领域**:
- **连接分析**: 连接池使用率、连接数趋势
- **查询性能**: 慢查询检测、响应时间分析
- **资源使用**: InnoDB效率、锁等待分析
- **复制健康**: 主从延迟、复制状态监控

**智能功能**:
- 并行策略同时执行多种分析方法
- 复杂表达式计算数据库性能指标
- ML模型检测异常连接和性能模式
- 自动化运维操作（如清理空闲连接）

### 4. 网络与安全智能分析 (network_security_intelligent_analysis.json)

**目的**: 网络流量、安全事件和异常行为的智能分析

**安全检测**:
- **威胁检测**: 暴力破解、DDoS、数据泄露检测
- **网络分析**: 延迟异常、带宽使用、连接模式
- **访问控制**: 权限提升、异常访问时间检测

**高级功能**:
- 表达式优先策略，灵活的安全规则
- 实时异常检测和风险评分
- 自动响应机制（IP封禁、账户锁定）
- 多级告警升级机制

## 分析策略说明

### 策略类型

1. **cascade**: 级联策略 - 按照优先级顺序执行：规则 → 表达式 → ML → 阈值
2. **parallel**: 并行策略 - 同时执行所有分析方法，综合结果
3. **rules_first**: 规则优先 - 优先使用规则引擎
4. **expr_first**: 表达式优先 - 优先使用表达式引擎
5. **ml_first**: 机器学习优先 - 优先使用ML算法
6. **threshold_first**: 阈值优先 - 优先使用传统阈值分析

### 组件协作

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   规则引擎      │    │   表达式引擎    │    │   机器学习      │
│                 │    │                 │    │                 │
│ • 快速决策      │    │ • 灵活计算      │    │ • 模式识别      │
│ • 已知问题      │    │ • 复杂条件      │    │ • 异常检测      │
│ • 并发执行      │    │ • 实时表达式    │    │ • 预测分析      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  智能分析器     │
                    │                 │
                    │ • 策略调度      │
                    │ • 结果融合      │
                    │ • 缓存管理      │
                    │ • 性能监控      │
                    └─────────────────┘
```

## 表达式引擎功能

### 内置函数

- **trend_analysis(values, window)**: 趋势分析
- **is_anomaly(value, threshold)**: 异常检测
- **health_score(metrics, weights)**: 健康评分
- **moving_average(values, window)**: 移动平均
- **statistical_analysis(values, method)**: 统计分析
- **percentile(values, p)**: 百分位数
- **threshold_check(value, min, max)**: 阈值检查

### 表达式示例

```javascript
// 系统健康评分
health_score([cpu_usage, memory_usage, disk_usage], [0.4, 0.4, 0.2])

// 异常检测
is_anomaly(cpu_usage, statistical_analysis(cpu_usage_history, 'threshold'))

// 趋势分析
trend_analysis(memory_usage_history, 20) == 'increasing' && memory_usage > 80

// 复合条件
cpu_usage > 85 && duration(cpu_usage > 80) > 300
```

## 机器学习算法

### 算法类型

1. **isolation_forest**: 隔离森林 - 适用于多维异常检测
2. **statistical**: 统计方法 - Z-score、IQR等统计检测
3. **iqr**: 四分位距方法 - 基于IQR的异常检测

### 配置示例

```json
{
  "ml": {
    "default_algorithm": "isolation_forest",
    "algorithms": {
      "isolation_forest": {
        "n_estimators": 100,
        "contamination": 0.1,
        "random_state": 42
      },
      "statistical": {
        "threshold": 2.5,
        "window_size": 20
      }
    }
  }
}
```

## 使用方法

### 1. 部署分析任务

```bash
# 复制分析配置到任务目录
cp examples/tasks/comprehensive_intelligent_analysis.json /etc/aiops/tasks/

# 重启调度器以加载新任务
systemctl restart aiops-scheduler
```

### 2. 配置数据源

确保以下指标数据可用：
- 系统指标：CPU、内存、磁盘、网络
- 数据库指标：连接数、QPS、慢查询等
- 安全事件：登录失败、访问日志等

### 3. 自定义规则和表达式

可以通过以下方式扩展分析能力：

1. **数据库规则**: 在`analysis_rules`表中添加新规则
2. **表达式文件**: 在`/etc/aiops/expressions/`目录添加表达式文件
3. **自定义函数**: 注册新的表达式函数

### 4. 监控分析结果

通过以下接口监控分析状态：
- `/api/v1/analysis/results` - 查看分析结果
- `/api/v1/analysis/stats` - 查看分析统计
- `/api/v1/tasks/{id}/status` - 查看任务状态

## 最佳实践

### 1. 策略选择

- **生产环境**: 使用`cascade`或`parallel`策略，确保全面分析
- **开发环境**: 使用`rules_first`，快速响应已知问题
- **资源受限**: 使用`threshold_first`，减少计算开销

### 2. 阈值设置

- 根据历史数据设置合理阈值
- 定期评估和调整阈值参数
- 考虑业务周期性变化

### 3. 告警策略

- 设置合理的告警冷却时间
- 使用分级告警避免告警风暴
- 结合自动化运维减少人工干预

### 4. 性能优化

- 启用缓存提高分析效率
- 合理设置并发参数
- 定期清理历史数据

## 故障排除

### 常见问题

1. **表达式编译失败**: 检查表达式语法和变量名
2. **ML模型加载失败**: 确保模型文件路径正确
3. **规则执行超时**: 调整`rule_timeout`参数
4. **内存使用过高**: 减少并发数或缓存大小

### 日志分析

```bash
# 查看分析器日志
tail -f /var/log/aiops/analyzer.log

# 查看表达式执行日志
grep "expression" /var/log/aiops/aiops.log

# 查看规则引擎日志
grep "rule_engine" /var/log/aiops/aiops.log
```

## 扩展开发

### 添加自定义分析器

1. 实现`Analyzer`接口
2. 在`Manager`中注册分析器
3. 创建对应的配置文件

### 自定义表达式函数

1. 在`custom_functions.go`中实现函数
2. 注册到表达式引擎
3. 更新函数文档

### 新增ML算法

1. 实现算法接口
2. 在ML配置中添加算法选项
3. 测试和验证算法效果

通过这些完整的分析示例，您可以快速了解和使用AIOps平台的智能分析功能，实现高效的智能运维管理。
