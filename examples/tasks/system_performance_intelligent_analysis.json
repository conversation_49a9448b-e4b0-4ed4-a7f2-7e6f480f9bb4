{"id": "system-performance-analysis-001", "name": "系统性能智能分析", "type": "analyze", "description": "结合规则引擎、表达式引擎和机器学习的系统性能分析", "schedule": {"type": "interval", "interval": "120s"}, "config": {"analyzer_type": "intelligent_analysis", "strategy": {"mode": "rules_first", "enable_rules": true, "enable_expression": true, "enable_ml": true, "enable_threshold": true, "timeout": "45s", "fallback_strategy": "expr_first"}, "rules": {"optimization_level": 2, "concurrent": true, "max_workers": 3, "enable_stats": true, "rule_timeout": "8s"}, "expression": {"database_enabled": true, "reload_cycle": "300s"}, "ml": {"default_algorithm": "statistical", "algorithms": {"statistical": {"threshold": 2.5, "window_size": 15}, "isolation_forest": {"contamination": 0.15}}, "enable_fallback": true}, "threshold": {"enabled": true, "threshold": 0.7}, "analysis_rules": [{"name": "高CPU使用率检测", "description": "检测CPU使用率异常高的情况", "condition": {"type": "expression", "expression": "cpu_usage > 85 && duration(cpu_usage > 80) > 300"}, "severity": "warning", "enabled": true, "priority": 1}, {"name": "内存泄漏检测", "description": "检测可能的内存泄漏模式", "condition": {"type": "expression", "expression": "trend_analysis(memory_usage, 10) == 'increasing' && memory_usage > 80"}, "severity": "critical", "enabled": true, "priority": 2}, {"name": "磁盘空间预警", "description": "磁盘空间不足预警", "condition": {"type": "threshold", "field": "disk_usage", "operator": ">", "value": 90}, "severity": "warning", "enabled": true, "priority": 3}], "expressions": [{"name": "system_health_score", "description": "系统健康评分计算", "expression": "health_score([cpu_usage, memory_usage, disk_usage], [0.4, 0.4, 0.2])", "return_type": "float", "enabled": true}, {"name": "performance_anomaly_detection", "description": "性能异常检测表达式", "expression": "is_anomaly(cpu_usage, statistical_analysis(cpu_usage_history, 'threshold')) || is_anomaly(memory_usage, statistical_analysis(memory_usage_history, 'threshold'))", "return_type": "boolean", "enabled": true}, {"name": "resource_trend_analysis", "description": "资源使用趋势分析", "expression": "{\n  \"cpu_trend\": trend_analysis(cpu_usage_history, 20),\n  \"memory_trend\": trend_analysis(memory_usage_history, 20),\n  \"prediction\": moving_average(cpu_usage_history, 5)\n}", "return_type": "object", "enabled": true}], "thresholds": [{"name": "CPU高使用率警告", "operator": ">", "value": 80.0, "severity": "warning", "description": "CPU使用率超过80%"}, {"name": "CPU高使用率紧急", "operator": ">", "value": 95.0, "severity": "critical", "description": "CPU使用率超过95%"}, {"name": "内存使用率警告", "operator": ">", "value": 85.0, "severity": "warning", "description": "内存使用率超过85%"}, {"name": "磁盘使用率警告", "operator": ">", "value": 90.0, "severity": "warning", "description": "磁盘使用率超过90%"}], "ml_models": [{"name": "cpu_anomaly_detector", "algorithm": "isolation_forest", "features": ["cpu_usage", "cpu_load_1min", "cpu_load_5min"], "training_window": "7d", "detection_threshold": 0.1}, {"name": "memory_pattern_analyzer", "algorithm": "statistical", "features": ["memory_usage", "memory_available", "swap_usage"], "window_size": 30, "z_score_threshold": 2.0}], "data_sources": {"system_metrics": {"cpu_usage": "latest_metric('cpu_usage_percent')", "memory_usage": "latest_metric('memory_usage_percent')", "disk_usage": "latest_metric('disk_usage_percent')", "network_io": "latest_metric('network_io_bytes')"}, "historical_data": {"cpu_usage_history": "historical_metrics('cpu_usage_percent', '1h')", "memory_usage_history": "historical_metrics('memory_usage_percent', '1h')"}}}, "alerts": {"enabled": true, "channels": ["email", "webhook"], "conditions": [{"trigger": "status == 'anomaly_detected' && severity in ['warning', 'critical']", "template": "system_performance_alert", "cooldown": "300s"}]}, "enabled": true, "tags": ["system", "performance", "intelligent", "analysis"]}