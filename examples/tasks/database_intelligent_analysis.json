{"id": "database-intelligent-analysis-001", "name": "数据库智能分析", "type": "analyze", "description": "MySQL数据库性能和健康状况的智能分析", "schedule": {"type": "interval", "interval": "180s"}, "config": {"analyzer_type": "intelligent_analysis", "strategy": {"mode": "parallel", "enable_rules": true, "enable_expression": true, "enable_ml": true, "enable_threshold": true, "timeout": "60s", "fallback_strategy": "cascade"}, "rules": {"optimization_level": 3, "concurrent": true, "max_workers": 4, "enable_stats": true, "rule_timeout": "12s"}, "expression": {"database_enabled": true, "reload_cycle": "600s"}, "ml": {"default_algorithm": "isolation_forest", "algorithms": {"isolation_forest": {"n_estimators": 150, "contamination": 0.05, "random_state": 42}, "statistical": {"threshold": 3.0, "window_size": 25}}}, "analysis_scenarios": {"connection_analysis": {"description": "数据库连接分析", "rules": [{"name": "连接数异常高", "condition": {"type": "expression", "expression": "mysql_connections > mysql_max_connections * 0.8"}, "severity": "warning", "action": "investigate_connections"}, {"name": "连接数超限", "condition": {"type": "threshold", "field": "mysql_connections", "operator": ">", "value": 500}, "severity": "critical", "action": "alert_dba"}], "expressions": [{"name": "connection_utilization", "expression": "(mysql_connections / mysql_max_connections) * 100", "description": "连接池利用率"}, {"name": "connection_trend", "expression": "trend_analysis(mysql_connections_history, 15)", "description": "连接数变化趋势"}]}, "query_performance": {"description": "查询性能分析", "rules": [{"name": "慢查询检测", "condition": {"type": "expression", "expression": "mysql_slow_queries > 10 && (mysql_slow_queries / mysql_total_queries) > 0.05"}, "severity": "warning"}, {"name": "查询响应时间异常", "condition": {"type": "expression", "expression": "mysql_avg_query_time > 1000 && is_anomaly(mysql_avg_query_time, statistical_analysis(mysql_avg_query_time_history, 'threshold'))"}, "severity": "critical"}], "expressions": [{"name": "query_performance_score", "expression": "health_score([mysql_qps, mysql_avg_query_time, mysql_slow_queries], [0.4, 0.3, 0.3])", "description": "查询性能综合评分"}, {"name": "query_efficiency", "expression": "mysql_qps / mysql_connections", "description": "查询效率（每连接QPS）"}]}, "resource_usage": {"description": "资源使用分析", "rules": [{"name": "InnoDB缓冲池命中率低", "condition": {"type": "expression", "expression": "mysql_innodb_buffer_pool_hit_rate < 95"}, "severity": "warning"}, {"name": "表锁等待严重", "condition": {"type": "expression", "expression": "mysql_table_locks_waited > mysql_table_locks_immediate * 0.1"}, "severity": "warning"}], "expressions": [{"name": "innodb_efficiency", "expression": "{\n  \"buffer_hit_rate\": mysql_innodb_buffer_pool_hit_rate,\n  \"buffer_usage\": (mysql_innodb_buffer_pool_bytes_used / mysql_innodb_buffer_pool_size) * 100,\n  \"lock_efficiency\": mysql_table_locks_immediate / (mysql_table_locks_immediate + mysql_table_locks_waited)\n}", "description": "InnoDB引擎效率指标"}]}, "replication_health": {"description": "主从复制健康检查", "rules": [{"name": "复制延迟过高", "condition": {"type": "threshold", "field": "mysql_slave_lag", "operator": ">", "value": 300}, "severity": "critical"}, {"name": "复制中断", "condition": {"type": "expression", "expression": "mysql_slave_running == false"}, "severity": "critical"}]}}, "thresholds": [{"name": "MySQL连接数警告", "field": "mysql_connections", "operator": ">", "value": 400, "severity": "warning"}, {"name": "MySQL QPS异常", "field": "mysql_qps", "operator": ">", "value": 10000, "severity": "warning"}, {"name": "慢查询过多", "field": "mysql_slow_queries", "operator": ">", "value": 50, "severity": "warning"}], "ml_analysis": {"models": [{"name": "mysql_connection_anomaly", "features": ["mysql_connections", "mysql_threads_connected", "mysql_threads_running"], "algorithm": "isolation_forest"}, {"name": "mysql_performance_pattern", "features": ["mysql_qps", "mysql_avg_query_time", "mysql_slow_queries"], "algorithm": "statistical"}], "detection_settings": {"sensitivity": "medium", "training_period": "7d", "evaluation_window": "1h"}}, "data_collection": {"metrics": ["mysql_connections", "mysql_max_connections", "mysql_qps", "mysql_avg_query_time", "mysql_slow_queries", "mysql_innodb_buffer_pool_hit_rate", "mysql_innodb_buffer_pool_size", "mysql_innodb_buffer_pool_bytes_used", "mysql_table_locks_immediate", "mysql_table_locks_waited", "mysql_slave_lag", "mysql_slave_running"], "collection_interval": "30s", "retention_period": "30d"}}, "alerts": {"enabled": true, "channels": ["email", "slack", "webhook"], "templates": {"database_critical": {"subject": "数据库紧急告警: {{.severity}} - {{.message}}", "body": "检测到数据库异常:\n时间: {{.timestamp}}\n严重级别: {{.severity}}\n描述: {{.message}}\n详细信息: {{.details}}"}, "database_warning": {"subject": "数据库性能警告: {{.message}}", "body": "数据库性能指标异常:\n{{.details}}"}}, "rules": [{"condition": "severity == 'critical'", "template": "database_critical", "channels": ["email", "slack"], "cooldown": "600s"}, {"condition": "severity == 'warning'", "template": "database_warning", "channels": ["email"], "cooldown": "1800s"}]}, "remediation": {"enabled": true, "auto_actions": [{"trigger": "mysql_connections > mysql_max_connections * 0.9", "action": "kill_idle_connections", "parameters": {"idle_timeout": 300, "max_kill_count": 10}}, {"trigger": "mysql_slow_queries > 100", "action": "analyze_slow_queries", "parameters": {"log_slow_queries": true, "generate_report": true}}]}, "enabled": true, "tags": ["database", "mysql", "performance", "intelligent", "analysis"]}