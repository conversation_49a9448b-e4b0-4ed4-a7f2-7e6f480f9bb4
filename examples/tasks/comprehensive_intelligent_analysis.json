{"id": "comprehensive-ai-analysis-001", "name": "综合智能分析", "type": "analyze", "description": "使用规则引擎、表达式引擎、机器学习和阈值分析的综合智能分析示例", "schedule": {"type": "interval", "interval": "300s"}, "config": {"analyzer_type": "intelligent_analysis", "strategy": {"mode": "cascade", "enable_rules": true, "enable_expression": true, "enable_ml": true, "enable_threshold": true, "timeout": "60s", "fallback_strategy": "parallel"}, "rules": {"optimization_level": 3, "concurrent": true, "max_workers": 4, "enable_stats": true, "rule_timeout": "10s", "rule_sources": [{"type": "database", "config": {"table": "analysis_rules"}, "priority": 1, "enabled": true}]}, "expression": {"database_enabled": true, "file_enabled": true, "expressions_path": "/etc/aiops/expressions", "reload_cycle": "300s"}, "ml": {"default_algorithm": "isolation_forest", "algorithms": {"isolation_forest": {"n_estimators": 100, "contamination": 0.1, "random_state": 42}, "statistical": {"threshold": 2.0, "window_size": 20}, "iqr": {"iqr_multiplier": 1.5}}, "model_path": "/var/aiops/models", "enable_fallback": true}, "threshold": {"enabled": true, "threshold": 0.5, "timeout": "10s"}, "cache": {"size": 1000, "ttl": "600s"}, "monitoring": {"enable_metrics": true, "metrics_port": 9090}, "metrics": ["cpu_usage_percent", "memory_usage_percent", "disk_usage_percent", "network_io_bytes", "mysql_connections", "mysql_queries_per_second"], "analysis_targets": {"system_health": {"description": "系统整体健康状况分析", "priority": 1, "enabled": true}, "performance_anomaly": {"description": "性能异常检测", "priority": 2, "enabled": true}, "resource_exhaustion": {"description": "资源耗尽预警", "priority": 3, "enabled": true}, "database_optimization": {"description": "数据库优化建议", "priority": 4, "enabled": true}}}, "enabled": true, "tags": ["intelligent", "comprehensive", "system", "database", "performance"], "metadata": {"author": "AIOps System", "version": "1.0.0", "created_at": "2025-05-29T00:00:00Z", "updated_at": "2025-05-29T00:00:00Z", "documentation": {"purpose": "演示AIOps平台的完整智能分析能力", "features": ["级联策略分析 - 按优先级依次执行不同分析方法", "规则引擎 - 基于预定义规则进行快速决策", "表达式引擎 - 灵活的表达式计算和条件判断", "机器学习 - 异常检测和模式识别", "阈值分析 - 传统阈值监控", "智能缓存 - 提高分析效率", "性能监控 - 分析过程的性能指标收集"], "use_cases": ["生产环境综合监控", "智能运维决策支持", "异常检测和预警", "性能优化建议", "容量规划辅助"], "strategy_explanation": {"cascade": "级联策略按照优先级顺序执行：规则 -> 表达式 -> 机器学习 -> 阈值，第一个有效结果即返回", "parallel": "并行策略同时执行所有分析方法，综合所有结果进行决策", "rules_first": "规则优先策略，优先使用规则引擎，失败时回退到其他方法", "expr_first": "表达式优先策略，优先使用表达式引擎进行分析", "ml_first": "机器学习优先策略，优先使用ML算法进行异常检测", "threshold_first": "阈值优先策略，优先使用传统阈值分析"}}}}