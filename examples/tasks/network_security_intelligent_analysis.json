{"id": "network-security-analysis-001", "name": "网络与安全智能分析", "type": "analyze", "description": "网络流量、安全事件和异常行为的智能分析", "schedule": {"type": "interval", "interval": "60s"}, "config": {"analyzer_type": "intelligent_analysis", "strategy": {"mode": "expr_first", "enable_rules": true, "enable_expression": true, "enable_ml": true, "enable_threshold": true, "timeout": "30s", "fallback_strategy": "ml_first"}, "rules": {"optimization_level": 3, "concurrent": true, "max_workers": 6, "enable_stats": true, "rule_timeout": "5s"}, "expression": {"database_enabled": true, "reload_cycle": "180s"}, "ml": {"default_algorithm": "isolation_forest", "algorithms": {"isolation_forest": {"n_estimators": 200, "contamination": 0.02, "random_state": 123}, "statistical": {"threshold": 3.5, "window_size": 50}}}, "security_analysis": {"threat_detection": {"description": "威胁检测和异常行为分析", "rules": [{"name": "异常登录检测", "condition": {"type": "expression", "expression": "failed_login_count > 10 && unique_source_ips > 5 && duration < 300"}, "severity": "critical", "tags": ["brute_force", "authentication"]}, {"name": "DDoS攻击检测", "condition": {"type": "expression", "expression": "network_requests_per_second > normal_baseline * 10 && unique_source_ips > 100"}, "severity": "critical", "tags": ["ddos", "network"]}, {"name": "数据泄露检测", "condition": {"type": "expression", "expression": "outbound_data_volume > historical_average(outbound_data_volume, 7) * 5"}, "severity": "critical", "tags": ["data_leak", "exfiltration"]}], "expressions": [{"name": "security_risk_score", "expression": "{\n  \"auth_risk\": (failed_login_count / total_login_attempts) * 100,\n  \"network_risk\": is_anomaly(network_traffic_volume, statistical_analysis(network_traffic_history, 'iqr')) ? 100 : 0,\n  \"access_risk\": (suspicious_access_count / total_access_count) * 100\n}", "description": "安全风险评分"}, {"name": "anomaly_detection", "expression": "{\n  \"traffic_anomaly\": is_anomaly(network_traffic_volume, network_baseline),\n  \"connection_anomaly\": is_anomaly(connection_count, connection_baseline),\n  \"bandwidth_anomaly\": is_anomaly(bandwidth_usage, bandwidth_baseline)\n}", "description": "网络异常检测"}]}, "network_analysis": {"description": "网络性能和流量分析", "rules": [{"name": "网络延迟异常", "condition": {"type": "expression", "expression": "network_latency > percentile(network_latency_history, 95) * 2"}, "severity": "warning"}, {"name": "带宽使用率过高", "condition": {"type": "threshold", "field": "bandwidth_utilization", "operator": ">", "value": 90}, "severity": "warning"}, {"name": "连接数异常", "condition": {"type": "expression", "expression": "active_connections > moving_average(active_connections_history, 20) * 3"}, "severity": "warning"}], "expressions": [{"name": "network_health_score", "expression": "health_score([network_latency, packet_loss_rate, bandwidth_utilization], [0.4, 0.3, 0.3])", "description": "网络健康评分"}, {"name": "traffic_pattern_analysis", "expression": "{\n  \"peak_hours\": time_series_peak_detection(network_traffic_24h),\n  \"trend\": trend_analysis(network_traffic_7d, 168),\n  \"seasonality\": detect_seasonality(network_traffic_30d)\n}", "description": "流量模式分析"}]}, "access_control": {"description": "访问控制和权限分析", "rules": [{"name": "权限提升检测", "condition": {"type": "expression", "expression": "privilege_escalation_attempts > 0 && user_role_changed == true"}, "severity": "critical"}, {"name": "异常访问时间", "condition": {"type": "expression", "expression": "access_hour < 6 || access_hour > 22 && user_type == 'internal'"}, "severity": "warning"}]}}, "thresholds": [{"name": "网络延迟警告", "field": "network_latency", "operator": ">", "value": 100, "severity": "warning"}, {"name": "带宽使用率警告", "field": "bandwidth_utilization", "operator": ">", "value": 85, "severity": "warning"}, {"name": "失败登录次数警告", "field": "failed_login_count", "operator": ">", "value": 5, "severity": "warning"}], "ml_models": [{"name": "network_traffic_anomaly", "algorithm": "isolation_forest", "features": ["network_traffic_volume", "packet_count", "connection_count", "bandwidth_usage"], "training_window": "14d", "detection_threshold": 0.05}, {"name": "security_behavior_analysis", "algorithm": "statistical", "features": ["login_frequency", "access_pattern", "data_transfer_volume", "command_execution_count"], "z_score_threshold": 3.0}], "data_sources": {"network_metrics": {"network_latency": "latest_metric('network_latency_ms')", "bandwidth_utilization": "latest_metric('bandwidth_utilization_percent')", "packet_loss_rate": "latest_metric('packet_loss_rate')", "active_connections": "latest_metric('active_connections')", "network_traffic_volume": "latest_metric('network_traffic_bytes')"}, "security_events": {"failed_login_count": "count_events('failed_login', '5m')", "suspicious_access_count": "count_events('suspicious_access', '10m')", "privilege_escalation_attempts": "count_events('privilege_escalation', '15m')"}, "historical_data": {"network_traffic_history": "historical_metrics('network_traffic_bytes', '24h')", "network_latency_history": "historical_metrics('network_latency_ms', '6h')", "active_connections_history": "historical_metrics('active_connections', '12h')"}}}, "alerts": {"enabled": true, "channels": ["email", "sms", "slack", "webhook"], "escalation": {"enabled": true, "levels": [{"severity": "warning", "delay": "0s", "channels": ["email"]}, {"severity": "critical", "delay": "0s", "channels": ["email", "sms", "slack"]}, {"severity": "critical", "delay": "300s", "channels": ["email", "sms", "slack", "webhook"], "condition": "not_acknowledged"}]}, "templates": {"security_incident": {"subject": "安全事件告警: {{.severity}} - {{.message}}", "body": "检测到安全事件:\n事件类型: {{.event_type}}\n时间: {{.timestamp}}\n严重级别: {{.severity}}\n描述: {{.message}}\n影响范围: {{.impact}}\n建议操作: {{.recommendation}}"}, "network_performance": {"subject": "网络性能告警: {{.message}}", "body": "网络性能异常:\n指标: {{.metric}}\n当前值: {{.current_value}}\n阈值: {{.threshold}}\n趋势: {{.trend}}"}}}, "auto_response": {"enabled": true, "actions": [{"trigger": "severity == 'critical' && tags.contains('ddos')", "action": "block_suspicious_ips", "parameters": {"block_duration": "3600s", "threshold": "requests_per_minute > 1000"}}, {"trigger": "failed_login_count > 20", "action": "temporary_account_lock", "parameters": {"lock_duration": "1800s", "notify_admin": true}}]}, "reporting": {"enabled": true, "reports": [{"name": "security_daily_summary", "frequency": "daily", "content": ["threat_summary", "anomaly_count", "response_actions"]}, {"name": "network_performance_weekly", "frequency": "weekly", "content": ["performance_trends", "capacity_analysis", "optimization_recommendations"]}]}, "enabled": true, "tags": ["network", "security", "threat-detection", "performance", "intelligent"]}