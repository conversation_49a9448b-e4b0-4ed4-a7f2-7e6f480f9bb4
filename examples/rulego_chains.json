{"chains": [{"id": "cpu_monitoring_chain", "name": "CPU监控处理链", "description": "处理CPU使用率监控、分析和告警", "version": "1.0", "ruleChain": {"ruleChain": {"name": "CPU监控处理链", "root": true, "debugMode": false, "nodes": [{"id": "input", "type": "input", "name": "监控数据输入", "debugMode": false, "configuration": {}}, {"id": "metricFilter", "type": "aiops/metricFilter", "name": "CPU指标过滤", "debugMode": false, "configuration": {"metric_name": "cpu_usage", "threshold": 0, "operator": ">=", "time_range": {"duration": "5m"}}}, {"id": "analysisNode", "type": "aiops/analysis", "name": "CPU使用率分析", "debugMode": false, "configuration": {"analysis_type": "threshold", "window_size": "5m", "algorithms": ["threshold", "trend"], "thresholds": {"cpu_usage": 80.0}}}, {"id": "thresholdCheck", "type": "aiops/threshold", "name": "CPU阈值检查", "debugMode": false, "configuration": {"metric_name": "cpu_usage", "thresholds": {"warning": 70.0, "critical": 90.0}, "operator": ">", "check_type": "single"}}, {"id": "anomalyDetection", "type": "aiops/anomaly", "name": "CPU异常检测", "debugMode": false, "configuration": {"algorithm": "zscore", "threshold": 2.0, "sensitivity": 0.8, "metric_name": "cpu_usage", "parameters": {"statistics": {"mean": 50.0, "std_dev": 15.0}}}}, {"id": "warningAlert", "type": "aiops/alert", "name": "CPU警告告警", "debugMode": false, "configuration": {"level": "warning", "title": "CPU使用率警告", "message": "CPU使用率超过警告阈值", "channels": ["email", "slack"], "template": "CPU使用率 {{current_value}}% 超过警告阈值 70%"}}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "type": "aiops/alert", "name": "CPU严重告警", "debugMode": false, "configuration": {"level": "critical", "title": "CPU使用率严重告警", "message": "CPU使用率严重过高，需要立即处理", "channels": ["<PERSON><PERSON><PERSON><PERSON>", "slack", "sms"], "template": "严重告警：CPU使用率 {{current_value}}% 超过严重阈值 90%"}}, {"id": "autoScale", "type": "aiops/scale", "name": "自动扩容", "debugMode": false, "configuration": {"namespace": "default", "resource_type": "deployment", "resource_name": "web-app", "scale_type": "horizontal", "min_replicas": 2, "max_replicas": 10, "scale_up_step": 2, "scale_down_step": 1, "cooldown_period": "5m", "conditions": {"scale_up_threshold": 80.0, "scale_down_threshold": 30.0, "metric_name": "cpu_usage"}}}, {"id": "logRecord", "type": "aiops/log", "name": "日志记录", "debugMode": false, "configuration": {"level": "info", "message": "CPU监控处理完成", "fields": ["metric_name", "value", "analysis", "threshold_check"], "format": "json", "target": "console", "template": "处理CPU指标 {{metric_name}}: 当前值={{current_value}}%, 状态={{analysis_status}}"}}], "connections": [{"fromIndex": 0, "toIndex": 1, "type": "Success"}, {"fromIndex": 1, "toIndex": 2, "type": "Success"}, {"fromIndex": 2, "toIndex": 3, "type": "Success"}, {"fromIndex": 3, "toIndex": 5, "type": "warning"}, {"fromIndex": 3, "toIndex": 6, "type": "critical"}, {"fromIndex": 6, "toIndex": 7, "type": "Success"}, {"fromIndex": 2, "toIndex": 4, "type": "Success"}, {"fromIndex": 4, "toIndex": 6, "type": "high_confidence_anomaly"}, {"fromIndex": 5, "toIndex": 8, "type": "Success"}, {"fromIndex": 6, "toIndex": 8, "type": "Success"}, {"fromIndex": 7, "toIndex": 8, "type": "Success"}]}}}, {"id": "memory_monitoring_chain", "name": "内存监控处理链", "description": "处理内存使用率监控和告警", "version": "1.0", "ruleChain": {"ruleChain": {"name": "内存监控处理链", "root": true, "nodes": [{"id": "input", "type": "input", "name": "内存数据输入"}, {"id": "memoryFilter", "type": "aiops/metricFilter", "name": "内存指标过滤", "configuration": {"metric_name": "memory_usage", "threshold": 0, "operator": ">="}}, {"id": "memoryThreshold", "type": "aiops/threshold", "name": "内存阈值检查", "configuration": {"metric_name": "memory_usage", "thresholds": {"warning": 80.0, "critical": 95.0}, "operator": ">"}}, {"id": "memory<PERSON><PERSON><PERSON>", "type": "aiops/alert", "name": "内存告警", "configuration": {"level": "warning", "title": "内存使用率告警", "message": "内存使用率过高", "channels": ["email"], "template": "内存使用率 {{current_value}}% 超过阈值"}}], "connections": [{"from": "input", "to": "memoryFilter"}, {"from": "memoryFilter", "to": "memoryThreshold"}, {"from": "memoryThreshold", "to": "memory<PERSON><PERSON><PERSON>", "label": "warning"}]}}}, {"id": "anomaly_detection_chain", "name": "异常检测处理链", "description": "通用异常检测和处理", "version": "1.0", "ruleChain": {"ruleChain": {"name": "异常检测处理链", "root": true, "nodes": [{"id": "input", "type": "input", "name": "数据输入"}, {"id": "anomalyDetector", "type": "aiops/anomaly", "name": "异常检测", "configuration": {"algorithm": "iqr", "sensitivity": 0.7, "threshold": 1.5, "parameters": {"quartiles": {"q1": 25.0, "q3": 75.0}}}}, {"id": "anomaly<PERSON><PERSON><PERSON>", "type": "aiops/alert", "name": "异常告警", "configuration": {"level": "warning", "title": "检测到异常", "message": "系统检测到异常行为", "channels": ["slack", "webhook"]}}, {"id": "dataTransform", "type": "aiops/transform", "name": "数据转换", "configuration": {"transformations": [{"type": "calculate", "source_field": "value", "target_field": "anomaly_score", "operation": "multiply", "parameters": {"value": 100}}], "output_format": "json"}}], "connections": [{"from": "input", "to": "anomalyDetector"}, {"from": "anomalyDetector", "to": "anomaly<PERSON><PERSON><PERSON>", "label": "anomaly"}, {"from": "anomalyDetector", "to": "dataTransform", "label": "Success"}]}}}]}